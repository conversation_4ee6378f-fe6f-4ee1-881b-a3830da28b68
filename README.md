# Crawler Server

一个基于 FastAPI 的高性能管理系统服务器，支持分布式缓存、JWT认证、角色权限控制、异常监控等企业级功能。

## 项目特性

- 🚀 **高性能**: 基于 FastAPI 异步框架构建，支持高并发请求
- 📦 **缓存系统**: 内置 Redis 缓存装饰器，支持分布式锁防止缓存击穿
- 🔐 **认证授权**: JWT 令牌认证，支持多角色权限控制和会话管理
- 🗄️ **数据库**: 使用 Tortoise ORM，支持 MySQL 数据库和多数据库连接
- 📊 **异常监控**: 完整的异常日志记录、操作日志追踪和用户行为监控
- 🛡️ **安全防护**: IP限流、用户限流、请求频率控制和安全认证
- 🐳 **容器化**: 完整的 Docker 容器化部署方案
- 🔄 **自动路由**: 智能的 API 路由自动发现和注册机制

## 项目结构

```
crawler-server/
├── docker-compose.yml      # Docker 编排配置
├── server/                 # 服务器代码目录
│   ├── Dockerfile         # Docker 镜像构建文件
│   ├── main.py            # 应用入口
│   ├── requirements.txt   # Python 依赖包
│   ├── pyproject.toml     # 项目配置
│   └── app/               # 应用核心代码
│       ├── __init__.py    # 应用工厂函数和路由自动注册
│       ├── config.py      # 动态配置管理
│       ├── static.py      # 静态配置和环境变量
│       ├── api/           # API 路由模块
│       │   ├── __init__.py    # 主路由注册
│       │   ├── base.py        # 基础响应模型和分页工具
│       │   └── resp.py        # 响应系统核心
│       ├── cache/         # 缓存模块
│       │   └── __init__.py    # Redis 缓存装饰器
│       ├── depends/       # 依赖注入模块
│       │   ├── auth.py        # JWT认证和权限控制
│       │   ├── log.py         # 操作日志记录
│       │   ├── rate_limiter.py # 限流器
│       │   └── real_ip.py     # 真实IP获取
│       ├── models/        # 数据模型
│       │   ├── __init__.py    # 模型初始化
│       │   ├── base.py        # 基础模型和配置模型
│       │   └── management.py  # 用户、日志等管理模型
│       └── utils/         # 工具函数
│           ├── __init__.py
│           └── patch.py       # 系统补丁
```

## 快速开始

### 环境要求

- Python 3.12+
- Docker & Docker Compose
- MySQL 8.0+
- Redis 7.0+

### Docker 部署

1. **克隆项目**
```bash
git clone <repository-url>
cd crawler-server
```

2. **配置环境变量**
```bash
# 创建 .env 文件
cat > .env << EOF
# MySQL 数据库配置
MYSQL_ROOT_PASSWORD=zcws
MYSQL_DATABASE=zcws
MYSQL_USER=zcws
MYSQL_PASSWORD=zcws

# 管理端配置
MANAGEMENT_DB_URL=mysql://zcws:zcws@db/zcws
MANAGEMENT_SECRET_KEY=your-256-bit-secret-key-here
MANAGEMENT_REDIS_PASSWORD=rmRmB77sxUpr

# Redis 配置
REDIS_PASSWORD=rmRmB77sxUpr
EOF
```

3. **启动服务**
```bash
# 启动所有服务
docker compose up -d

# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f server
```

4. **访问服务**
- API 服务: http://localhost:8020
- API 文档: http://localhost:8020/docs
- MySQL: localhost:3308

## 核心功能使用指南

### 1. 缓存装饰器

项目内置了强大的缓存装饰器，支持分布式锁和自动过期：

```python
from app.cache import cached_before

@cached_before("user_info", timeout=1800)
async def get_user_info(user_id: int, include_profile: bool = True):
    # 模拟从数据库获取用户信息
    user_data = await User.get(id=user_id)
    if include_profile:
        # 获取用户详细信息
        profile_data = await get_user_profile(user_id)
        user_data.update(profile_data)
    return user_data

# 使用示例：
# 第一次调用，会执行函数并缓存结果
user = await get_user_info(123, include_profile=True)

# 第二次调用相同参数，直接从缓存返回
user = await get_user_info(123, include_profile=True)

# 删除缓存
await get_user_info(123, include_profile=True, delete=True)
```

**缓存装饰器特性:**
- 自动根据函数参数生成缓存键
- 分布式锁防止缓存击穿和缓存雪崩
- 支持手动删除缓存
- 可配置过期时间
- 使用 pickle 序列化，支持复杂数据类型

### 2. 高级响应系统

项目采用了创新的响应系统设计，支持多种响应类型组合：

```python
from app.api.resp import ResponseBase, assign_response, MessageType, NotificationType, ResponseType
from app.api.base import BaseResp, BasePageResp
from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional

# 定义数据模型
class UserBaseModel(BaseModel):
    username: str = Field(title="用户名")
    nickname: str = Field(title="昵称")
    email: str = Field(title="邮箱")
    phone: Optional[str] = Field(default=None, title="手机号")
    roles: list[str] = Field(title="角色")

class UserRespModel(UserBaseModel):
    id: int = Field(title="用户ID")
    enabled: bool = Field(title="用户启用")
    created_at: datetime = Field(title="注册日期")

# 基础响应类定义
@assign_response(True, MessageType.SUCCESS, "用户信息获取成功")
class GetUserResponse(ResponseBase): ...

@assign_response(True, MessageType.SUCCESS, "用户列表查询成功")
class UserListResp(ResponseBase): ...

# 错误响应类定义
@assign_response(False, MessageType.ERROR, "用户不存在")
class UserNotFoundError(ResponseBase): ...

# 组合响应类型 - 同时显示消息和通知
@assign_response(False, MessageType.ERROR | NotificationType.ERROR, "登录失败")
class LoginFailedError(ResponseBase): ...

# 复杂组合 - 错误消息 + HTTP状态码
@assign_response(False, MessageType.ERROR | ResponseType.UNAUTHORIZED, "访问被拒绝")
class AccessDeniedError(ResponseBase): ...

# 单个用户API - 使用 BaseResp[UserRespModel]
@router.get("/user/{user_id}", response_model=BaseResp[UserRespModel])
async def get_user(user_id: int):
    user = await User.get_or_none(id=user_id, deleted_at=None)
    if not user:
        raise UserNotFoundError()  # 可以直接抛出响应异常
    return GetUserResponse(user)

# 用户列表API - 使用 BasePageResp[UserRespModel]
@router.get("/users", response_model=BasePageResp[UserRespModel], name="用户列表查询")
async def get_user_list(
    page_size: int = Query(default=20, description="每页数据量"),
    page_no: int = Query(default=1, description="页号", ge=1),
    enabled: bool = Query(default=None, description="账号是否禁用"),
    search: str = Query(default=None, description="账号/邮箱/昵称"),
    begin: datetime = Query(default=None, description="注册时间下界"),
    end: datetime = Query(default=None, description="注册时间上界"),
    role: Optional[str] = Query(default=None, description="角色过滤"),
):
    q = User.filter(deleted_at=None)
    if enabled is not None:
        q = q.filter(enabled=enabled)
    if search:
        q = q.filter(Q(username__contains=search) | Q(nickname__contains=search) | Q(email__contains=search))
    if begin:
        q = q.filter(created_at__gt=begin.date())
    if end:
        q = q.filter(created_at__lt=datetime.date(end + timedelta(days=1)))
    if role:
        q = q.filter(roles__contains=role)

    result, page_no, total = await get_page_result(page_size, page_no, q)
    # 直接返回字典，系统会自动转换成标准的PageResponse
    return {"items": result, "page_no": page_no, "total": total}
```

**响应系统特性:**

1. **多种响应类型支持**:
   - `MessageType`: 前端消息提示 (SUCCESS, ERROR, WARNING, INFO, SILENT)
   - `NotificationType`: 前端通知弹窗 (SUCCESS, ERROR, WARNING, INFO)
   - `ResponseType`: 响应类型枚举，用于标识系统级别的响应处理 (MANUAL, INTERNAL_SERVER_ERROR, UNAUTHORIZED)

2. **响应类型组合**:
   - 单一类型: `MessageType.SUCCESS`
   - 组合类型: `MessageType.ERROR | NotificationType.ERROR`
   - 复杂组合: `MessageType.ERROR | ResponseType.UNAUTHORIZED`

3. **单个数据响应**: 使用 `BaseResp[DataModel]`
   - `DataModel` 是要返回的数据的 Pydantic 模型
   - 直接返回响应类实例: `return GetUserResponse(user_data)`
   - 支持抛出异常: `raise UserNotFoundError()`

4. **分页数据响应**: 使用 `BasePageResp[ItemModel]`
   - `ItemModel` 是列表中单个项目的 Pydantic 模型
   - 返回包含分页信息的字典: `{"items": result, "page_no": page_no, "total": total}`
   - 系统会自动转换成标准的分页响应格式

5. **响应类定义最佳实践**:
   - 使用 `@assign_response` 装饰器定义响应类型和消息
   - 支持成功和错误两种响应状态
   - 响应类继承自 `Exception`，可直接抛出
   - 自动分配唯一的响应代码

### 3. JWT认证和权限控制

项目采用 JWT 令牌认证，支持多角色权限控制：

```python
from app.depends.auth import get_current_user, check_role_is, check_role_in
from fastapi import Depends

# 需要登录的接口
@router.get("/profile")
async def get_profile(current_user: User = Depends(get_current_user)):
    return {"user": current_user}

# 需要特定角色的接口
@router.get("/admin/users", dependencies=[check_role_is("super")])
async def admin_get_users(current_user: User = Depends(get_current_user)):
    # 只有超级管理员可以访问
    return await get_all_users()

# 需要多个角色之一的接口
@router.get("/manager/reports", dependencies=[check_role_in(["super", "admin", "manager"])])
async def get_reports(current_user: User = Depends(get_current_user)):
    # 超级管理员、管理员或经理可以访问
    return await get_reports_data()
```

**认证特性:**
- JWT 访问令牌和刷新令牌机制
- Redis 会话管理，支持令牌撤销
- 多角色权限控制 (super, admin, user等)
- 用户状态检查 (enabled/disabled)
- 自动处理认证异常和权限不足

### 4. 数据模型系统

使用 Tortoise ORM 定义模型，支持多数据库连接：

```python
from app.models.management import User, OperationLog, ExceptionLog
from app.models.base import Config, DBBaseModel
from tortoise import fields

# 用户模型 (management 数据库)
class User(DBBaseModel):
    class Meta:
        table = "user"
        app = "management"

    username = fields.CharField(max_length=100, unique=True, description="用户名")
    nickname = fields.CharField(max_length=100, description="昵称")
    email = fields.CharField(max_length=256, unique=True, description="邮箱")
    password = fields.CharField(max_length=128, description="密码")
    phone = fields.CharField(max_length=30, unique=True, null=True, description="手机号")
    enabled = fields.BooleanField(default=True, description="用户启用")
    roles = fields.JSONField(default=["user"], description="角色")
    deleted_at = fields.DatetimeField(default=None, null=True, description="删除日期")

# 配置模型 (默认数据库)
class Config(DBBaseModel):
    class Meta:
        table = "config"

    key = fields.CharField(max_length=100, unique=True, description="配置键")
    value = fields.TextField(description="配置值")
```

**数据模型特性:**
- 基础模型 `DBBaseModel` 包含通用字段 (id, uuid, created_at, updated_at)
- 自动密码加密和验证
- 软删除支持 (deleted_at)
- JSON 字段支持复杂数据类型
- 多数据库连接配置

### 5. 多层限流系统

项目提供了完整的限流保护机制：

```python
from app.depends.rate_limiter import limit_ip, limit_user, ban_ip
from fastapi import Depends

# IP 限流 - 基于客户端IP地址
@router.post("/api/sensitive")
async def sensitive_api(
    _=Depends(limit_ip("sensitive_api", time=60, freq=10))  # 每分钟最多10次调用
):
    return {"message": "success"}

# 用户限流 - 基于登录用户
@router.post("/api/user_action")
async def user_action(
    _=Depends(limit_user("user_action", time=300, freq=20))  # 5分钟内最多20次操作
):
    return {"message": "success"}

# IP 封禁 - 达到阈值后封禁IP
@router.post("/api/login")
async def login_api(
    data: LoginRequest,
    _=Depends(limit_ip("login", time=300, freq=5, show_left=True)),  # 登录限流
    __=Depends(ban_ip("login_fail", freq=10))  # 失败10次后封禁IP
):
    # 登录逻辑
    return {"message": "login success"}
```

**限流器类型和参数:**

1. **IP限流** (`limit_ip`):
   - `key`: 限流标识符，用于区分不同的API
   - `time`: 时间窗口（秒）
   - `freq`: 频率限制（次数）
   - `show_left`: 是否显示剩余时间，默认True

2. **用户限流** (`limit_user`):
   - 基于登录用户ID进行限流
   - 需要用户已认证
   - 参数与IP限流相同

3. **IP封禁** (`ban_ip`):
   - `key`: 封禁标识符
   - `freq`: 触发封禁的次数阈值
   - `show_left`: 是否显示解封剩余时间

**限流特性:**
- 基于 Redis 实现分布式限流
- 支持 IP 和用户两个维度
- 自动获取真实客户端IP (支持CDN和代理)
- 分布式锁防止并发问题
- 友好的错误提示信息

## 配置管理

### 环境变量配置

项目使用环境变量进行配置管理：

```python
# app/static.py
from os import getenv

# 数据库配置
MYSQL_URL = getenv("MYSQL_URL", "mysql://zcws:zcws@db/zcws")
MANAGEMENT_DB_URL = getenv("MANAGEMENT_DB_URL")

# 管理端配置
MANAGEMENT_SECRET_KEY = getenv("MANAGEMENT_SECRET_KEY", "00" * 128)
MANAGEMENT_SESSION_REDIS_URL = "redis://cache:6379/0"
MANAGEMENT_REDIS_PASSWORD = getenv("MANAGEMENT_REDIS_PASSWORD", REDIS_PASSWORD)

# Redis 配置
CACHE_REDIS_URL = "redis://cache:6379/1"
REDIS_PASSWORD = getenv("REDIS_PASSWORD", "rmRmB77sxUpr")

# JWT 配置
JWT_ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24      # 访问令牌过期时间 (24小时)
JWT_REFRESH_TOKEN_EXPIRE_MINUTES = 60 * 24 * 7  # 刷新令牌过期时间 (7天)
JWT_ALGORITHM = "HS256"                          # 加密算法
```

### 数据库配置

项目支持多数据库连接，使用 Tortoise ORM 配置：

```python
# app/static.py
TORTOISE_CONFIG = {
    "connections": {
        "default": MYSQL_URL,                    # 主数据库
        "management": MANAGEMENT_DB_URL,         # 管理数据库
    },
    "apps": {
        "models": {
            "models": ["app.models.base"],
            "default_connection": "default",
        },
        "management": {
            "models": ["app.models.management"],
            "default_connection": "management",
        },
    }
}
```

### 动态配置系统

支持数据库存储的动态配置：

```python
from app.config import Mail, System, update_config

# 获取邮件配置
mail_config = await Mail()
print(mail_config.MAIL_SERVER)

# 获取系统配置
system_config = await System()
print(system_config.SYSTEM_NAME)

# 更新配置
await update_config({
    "SYSTEM_NAME": "新系统名称",
    "ENABLE_REGISTER": True
})
```

## 开发指南

### 自动路由注册系统

项目采用智能的路由自动发现机制，无需手动注册路由：

1. **在 `app/api/` 目录下创建新模块**：

```python
# app/api/my_feature/__init__.py
from fastapi import APIRouter
from app.api.resp import ResponseBase, assign_response, MessageType
from app.api.base import BaseResp

router = APIRouter(tags=["我的功能"])

@assign_response(True, MessageType.SUCCESS, "功能测试成功")
class MyFeatureResponse(ResponseBase): ...

@router.get("/test", response_model=BaseResp[dict], name="功能测试")
async def my_feature_test():
    return MyFeatureResponse(data={"message": "Hello World"})
```

2. **系统会自动发现并注册路由**：
   - 路由会自动注册到 `/api/my_feature/` 前缀下
   - 支持嵌套目录结构
   - 自动处理路由冲突和优先级

3. **路由注册特性**：
   - 递归扫描 `app/api/` 目录
   - 自动导入包含 `router` 的模块
   - 支持依赖注入和中间件
   - 自动生成 API 文档

### 添加新的数据模型

1. **选择合适的模型文件**：
   - `app/models/base.py`: 基础配置和通用模型
   - `app/models/management.py`: 用户管理相关模型

2. **定义新模型**：

```python
# app/models/management.py
from .base import DBBaseModel
from tortoise import fields

class MyModel(DBBaseModel):  # 继承基础模型
    class Meta:
        table = "my_models"
        app = "management"  # 指定数据库连接

    name = fields.CharField(max_length=100, description="名称")
    status = fields.BooleanField(default=True, description="状态")
    config = fields.JSONField(default={}, description="配置信息")
```

3. **在 `app/models/__init__.py` 中导入**：

```python
from .management import User, OperationLog, ExceptionLog, MyModel
```

4. **模型特性**：
   - 自动包含 id, uuid, created_at, updated_at 字段
   - 支持软删除 (deleted_at)
   - 自动密码加密 (User模型)
   - JSON 字段支持复杂数据

### 添加依赖注入

在 `app/depends/` 目录下创建依赖函数：

```python
# app/depends/my_dependency.py
from fastapi import Depends, HTTPException, Request
from app.depends.auth import get_current_user
from app.depends.real_ip import get_ip
from app.models import User

async def validate_request_data(data: dict):
    if not data.get("required_field"):
        raise HTTPException(status_code=400, detail="缺少必填字段")
    return data

async def get_user_with_permission(
    permission: str,
    current_user: User = Depends(get_current_user)
):
    if permission not in current_user.roles:
        raise HTTPException(status_code=403, detail="权限不足")
    return current_user

async def log_request_info(
    request: Request,
    ip: str = Depends(get_ip),
    user: User = Depends(get_current_user)
):
    # 记录请求信息
    print(f"User {user.username} from {ip} accessed {request.url.path}")
    return {"user": user, "ip": ip, "path": request.url.path}
```

### 异常处理和监控

项目提供了完整的异常处理和监控系统：

```python
from app.api.resp import ResponseBase, assign_response, MessageType, NotificationType

# 定义业务异常响应
@assign_response(False, MessageType.ERROR, "用户不存在")
class UserNotFoundError(ResponseBase): ...

@assign_response(False, MessageType.ERROR | NotificationType.ERROR, "权限不足")
class PermissionDeniedError(ResponseBase): ...

@assign_response(False, MessageType.WARNING, "参数格式错误")
class InvalidParameterError(ResponseBase): ...

# 在 API 中使用异常处理
@router.get("/user/{user_id}")
async def get_user(user_id: int):
    user = await User.get_or_none(id=user_id, deleted_at=None)
    if not user:
        raise UserNotFoundError()  # 直接抛出响应异常

    # 检查权限
    if not user.enabled:
        raise PermissionDeniedError("用户已被禁用")

    return SuccessResponse(user)

@router.post("/user/update")
async def update_user(data: UpdateUserRequest):
    if not data.email or "@" not in data.email:
        raise InvalidParameterError("邮箱格式不正确")

    # 业务逻辑
    return UpdateSuccess()
```

**异常处理特性:**
- 响应类继承自 `Exception`，可直接抛出
- 自动记录异常信息到 `ExceptionLog` 模型
- 支持自定义错误描述信息
- 统一的错误响应格式
- 支持组合消息类型（ERROR + NOTIFICATION）

**全局异常监控:**
项目内置全局异常处理器，自动捕获并记录所有异常：
- 用户信息（如果已认证）
- 客户端真实IP地址
- 请求方法和完整路径
- 请求载荷和参数
- 完整的异常堆栈信息
- 异常发生时间和请求UUID

**操作日志系统:**
```python
from app.depends.log import Operation, operation_log

# 手动记录操作日志
@router.post("/user/create")
async def create_user(data: CreateUserRequest):
    operation = Operation("用户管理", current_user, ip)
    try:
        user = await User.create(**data.dict())
        await operation.success(f"用户 {user.username} 创建成功")
        return CreateSuccess(user)
    except Exception as e:
        await operation.fail(f"用户创建失败: {str(e)}")
        raise

# 使用装饰器自动记录
@router.put("/user/{user_id}")
async def update_user(
    user_id: int,
    data: UpdateUserRequest,
    op: Operation = operation_log("用户管理-更新")
):
    user = await User.get(id=user_id)
    await user.update_from_dict(data.dict())
    await op.success(f"用户 {user.username} 更新成功")
    return UpdateSuccess()
```

## 技术栈

- **Web框架**: FastAPI 0.115.6
- **数据库ORM**: Tortoise ORM 0.23.0
- **数据库**: MySQL 8.0
- **缓存**: Redis 7.0
- **认证**: JWT (python-jose)
- **数据验证**: Pydantic 2.10.5
- **异步支持**: asyncio, asyncmy
- **容器化**: Docker, Docker Compose
- **Python版本**: 3.13+
