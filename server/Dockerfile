FROM python:3.13-bookworm
RUN mkdir -p /server /data/uploads
WORKDIR /server

COPY sources.list /etc/apt/sources.list
RUN rm /etc/apt/sources.list.d/debian.sources
RUN apt update && apt install -y vim && apt-get autoclean; rm -rf /var/lib/apt/lists/*

COPY requirements.txt /server/requirements.txt
RUN python -m pip install --upgrade pip --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple; \
    pip install -r requirements.txt --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple
COPY . /server
RUN chmod +x /server/docker-entrypoint.sh
#ENTRYPOINT sleep 10; python3 -m uvicorn main:app --reload --host 0.0.0.0
ENTRYPOINT ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--use-colors"]
