from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordBearer
from jose import jwt, JWTError
from loguru import logger

from app.api.resp import ResponseBase, MessageType, ResponseType, assign_response
from app.cache import sessionRedis
from app.models import User
from app.static import MANAGEMENT_SECRET_KEY, JWT_ALGORITHM

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/login", auto_error=False)


@assign_response(False, ResponseType.UNAUTHORIZED, "未登录")
class UnauthorizedResponse(ResponseBase): ...


@assign_response(False, MessageType.ERROR, "您已被封禁")
class ForbiddenResponse(ResponseBase): ...


@assign_response(False, MessageType.ERROR, "用户不存在")
class UserNotFoundResponse(ResponseBase): ...


async def get_current_user(token: str | None = Depends(oauth2_scheme)):
    if token is None:
        raise UnauthorizedResponse
    try:
        payload = jwt.decode(token, MANAGEMENT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        sub: str = payload.get("sub")
        if sub is None:
            raise UnauthorizedResponse
        if not sub.startswith("user:"):
            raise UnauthorizedResponse

        user_id = int(sub.split(":")[1])
        if user_id is None:
            raise UserNotFoundResponse
    except JWTError:
        raise UnauthorizedResponse
    user = await User.get_or_none(id=user_id, deleted_at=None)
    if user is None:
        raise UnauthorizedResponse
    session = await sessionRedis.get(f"user:{user.id}")
    if session is None or session.decode() != token:
        raise UnauthorizedResponse
    if not user.enabled:
        raise ForbiddenResponse

    return user


def check_role_in(roles: list[str] = ["super"]) -> Depends:
    async def check_role(current_user: User = Depends(get_current_user)):
        for role in roles:
            if role in current_user.roles:
                return current_user
        raise HTTPException(status_code=403, detail="权限不足")

    return Depends(check_role)


def check_role_is(role: str = "super") -> Depends:
    async def check_role(current_user: User = Depends(get_current_user)):
        if role in current_user.roles:
            return current_user
        raise HTTPException(status_code=403, detail="权限不足")

    return Depends(check_role)
