from asyncio import sleep
from datetime import datetime
from functools import partial
from math import ceil
from pickle import HIGHEST_PROTOCOL, dumps, loads

from fastapi import HTTPEx<PERSON>, Depends
from starlette.requests import Request

from app.cache import cacheRedis
from app.depends.auth import get_current_user
from app.depends.real_ip import get_ip
from app.models import User

_dumps = partial(dumps, protocol=HIGHEST_PROTOCOL)
_loads = partial(loads)


def limit_ip(key: str, time: int, freq: int, show_left=True):
    def make_key(request):
        ip = get_ip(request)
        return f"cache-ip-{key}-{ip}", f"cache-ip-{key}-{ip}-lock"

    async def depends_func(request: Request):
        cache_key, lock_key = make_key(request)
        rv = await cacheRedis.get(cache_key)
        if rv is not None:
            rv = int(rv.decode())
            if rv >= freq:
                if show_left:
                    ttl = await cacheRedis.pttl(cache_key)
                    raise HTTPException(429, f"请求过快, 请{ceil(ttl/1000)}秒后重试")
                else:
                    raise HTTPException(429, "请求过快")
            await cacheRedis.incrby(cache_key)
            return

        lock = cacheRedis.lock(lock_key, timeout=10)
        while rv is None:
            if not await lock.acquire(blocking=False):
                await sleep(0.01)
                continue
            await cacheRedis.set(cache_key, 0, ex=time)
            try:
                await lock.release()
            except:
                pass
            break
        await cacheRedis.incrby(cache_key)

    return depends_func


def ban_ip(key: str, freq: int, show_left=True):
    def make_key(request):
        ip = get_ip(request)
        return f"cache-ban-ip-{key}-{ip}"

    async def depends_func(request: Request):
        cache_key = make_key(request)
        rv = await cacheRedis.get(cache_key)
        if rv is not None:
            rv = int(rv.decode())
            if rv >= freq:
                if show_left:
                    ttl = await cacheRedis.pttl(cache_key)
                    raise HTTPException(
                        429, f"您的IP已被封禁, 请{ceil(ttl/1000)}秒后重试"
                    )
                else:
                    raise HTTPException(429, "您的IP已被封禁，请稍后再访问")

    return depends_func


async def ban_ip_add(request: Request, key: str, time: int):
    ip = get_ip(request)
    cache_key = f"cache-ban-ip-{key}-{ip}"
    lock_key = f"cache-ban-ip-{key}-{ip}-lock"

    lock = cacheRedis.lock(lock_key, timeout=10)
    rv = await cacheRedis.get(cache_key)

    while rv is None:
        if not await lock.acquire(blocking=False):
            await sleep(0.01)
            continue
        await cacheRedis.set(cache_key, 0, ex=time)
        try:
            await lock.release()
        except:
            pass
        break
    await cacheRedis.incrby(cache_key)


def limit_user(key: str, time: int, freq: int, show_left=True):
    def make_key(user):
        return f"cache-user-{key}-{user.id}"

    async def depends_func(user: User = Depends(get_current_user)):
        cache_key = make_key(user)
        rv = await cacheRedis.get(cache_key)
        if rv is not None:
            rv = int(rv.decode())
            if rv >= freq:
                if show_left:
                    ttl = await cacheRedis.pttl(cache_key)
                    raise HTTPException(429, f"请求过快, 请{ceil(ttl/1000)}秒后重试")
                else:
                    raise HTTPException(429, "请求过快")
            await cacheRedis.incrby(cache_key)
            return
        while rv is None:
            lock = cacheRedis.lock(cache_key, timeout=10)
            if not await lock.acquire(blocking=False):
                await sleep(0.01)
                continue
            await cacheRedis.set(cache_key, 0, ex=time)
            break
        await cacheRedis.incrby(cache_key)

    return depends_func


async def limit_user_reset(user: User, key: str):
    cache_key = f"cache-user-{key}-{user.id}"
    await cacheRedis.delete(cache_key)


async def limit_per_day(key: str, limit: int) -> bool:
    date_str = datetime.now().strftime("%Y-%m-%d")
    cache_key = f"limit-per-day-{key}-{date_str}"
    count = await cacheRedis.incr(cache_key)
    if count == 1:
        await cacheRedis.expire(cache_key, 86400)
    if count > limit:
        return False
    return True
