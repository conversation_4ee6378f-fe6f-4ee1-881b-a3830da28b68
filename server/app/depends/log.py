from typing import Optional

from fastapi import Depends, Request

from app.depends.auth import get_current_user
from app.depends.real_ip import get_ip
from app.models import OperationLog, User


class Operation:
    def __init__(self, module: str, user: User, ip: str = ""):
        self.module = module
        self.user = user
        self.ip = ip

    async def success(self, info: str):
        await OperationLog.create(
            user_id=self.user.id,
            ip=self.ip,
            module=self.module,
            info=info,
            status=1,
        )

    async def fail(self, info: str):
        await OperationLog.create(
            user_id=self.user.id,
            ip=self.ip,
            module=self.module,
            info=info,
            status=0,
        )


def operation_log(module: str):
    async def f(
        ip: Optional[str] = Depends(get_ip), user: User = Depends(get_current_user)
    ):
        return Operation(module, user, ip)

    return Depends(f)
