from os import getenv

CACHE_REDIS_URL: str = "redis://cache:6379/1"

REDIS_PASSWORD: str = getenv("REDIS_PASSWORD", "rmRmB77sxUpr")

MYSQL_URL: str = getenv("MYSQL_URL", "mysql://zcws:zcws@db/zcws")

# 管理端配置
MANAGEMENT_DB_URL: str = getenv("MANAGEMENT_DB_URL")
MANAGEMENT_SECRET_KEY: str = getenv("MANAGEMENT_SECRET_KEY", "00" * 128)
MANAGEMENT_SESSION_REDIS_URL: str = getenv("MANAGEMENT_SESSION_REDIS_URL")
MANAGEMENT_REDIS_PASSWORD: str = getenv("MANAGEMENT_REDIS_PASSWORD", REDIS_PASSWORD)

# JWT配置
JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24
JWT_REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7
JWT_ALGORITHM: str = "HS256"

TORTOISE_CONFIG = {
    "connections": {
        "default": MYSQL_URL,
        "management": MANAGEMENT_DB_URL,
    },
    "apps": {
        "models": {
            "models": ["app.models"],
            "default_connection": "default",
        },
        "management": {
            "models": ["app.models"],
            "default_connection": "management",
        },
    },
}
