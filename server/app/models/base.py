from datetime import datetime
from uuid import uuid4

from tortoise import Model
from tortoise.fields import (
    IntField,
    UUIDField,
    CharField,
    DatetimeField,
    TextField,
)


class DBBaseModel(Model):
    class Meta:
        abstract = True

    id = IntField(pk=True)
    uuid = UUIDField(index=True, default=uuid4)
    created_at: datetime = DatetimeField(auto_now_add=True, description="注册日期")
    updated_at: datetime = DatetimeField(auto_now=True, description="更新日期")

    def __str__(self):
        return f"{self.id}"


class Config(Model):
    class Meta:
        table = "config"

    key = CharField(max_length=50, primary_key=True)
    value = TextField()
