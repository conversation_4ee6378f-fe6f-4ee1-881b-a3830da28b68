from datetime import datetime
from typing import Optional
from uuid import uuid4

from tortoise import Model
from tortoise.fields import (
    UUIDField,
    CharField,
    BooleanField,
    DatetimeField,
    TextField,
    JSONField,
    ForeignKeyField,
    SmallIntField,
    OnDelete,
)

from .base import DBBaseModel


class User(DBBaseModel):
    class Meta:
        table = "user"
        ordering = ["id"]
        app = "management"

    username = Cha<PERSON><PERSON><PERSON>(max_length=100, unique=True, description="用户名")
    nickname = Char<PERSON><PERSON>(max_length=100, description="昵称")
    email = CharField(max_length=256, unique=True, description="邮箱")
    password = CharField(max_length=128, description="密码")
    phone = CharField(max_length=30, unique=True, null=True, description="手机号")
    avatar = CharField(default=None, max_length=50, null=True, description="头像")
    enabled = BooleanField(default=True, description="用户启用")
    roles: list[str] = JSONField(default=["user"], description="角色")
    deleted_at = DatetimeField(default=None, null=True, description="删除日期")


class OperationLog(DBBaseModel):
    class Meta:
        table = "operation_log"
        ordering = ["-id"]
        app = "management"

    user = ForeignKeyField(
        "management.User",
        related_name="user_operation_logs",
        on_delete=OnDelete.CASCADE,
        description="用户",
    )
    user_id: int
    ip = CharField(max_length=42, description="IP")
    module = CharField(max_length=255, description="模块")
    info = TextField(description="信息")
    status = SmallIntField(default=0, description="状态 0 失败 1 成功")


class ExceptionLog(DBBaseModel):
    class Meta:
        table = "exception_log"
        ordering = ["-id"]
        app = "management"

    user = ForeignKeyField(
        "management.User",
        null=True,
        related_name="user_exception_logs",
        on_delete=OnDelete.CASCADE,
        description="用户",
    )
    user_id: Optional[int]
    ip = CharField(max_length=42, description="IP")
    path = CharField(max_length=255, description="请求路径")
    method = CharField(max_length=10, description="请求方法")
    payload = TextField(description="请求参数")
    traceback = TextField(description="异常信息")


class AttachmentModel(Model):
    class Meta:
        table = "attachment"
        app = "management"

    # id = IntField(pk=True)
    uuid = UUIDField(pk=True, default=uuid4)
    name = CharField(max_length=255, description="名称")
    created_at: datetime = DatetimeField(auto_now_add=True, description="注册日期")
    updated_at: datetime = DatetimeField(auto_now=True, description="更新日期")

    def __str__(self):
        return f"{self.uuid}"
