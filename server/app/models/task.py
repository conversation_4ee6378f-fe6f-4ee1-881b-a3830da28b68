from enum import StrEnum

from tortoise.fields import (
    IntField,
    CharField,
    BooleanField,
    DatetimeField,
    TextField,
    JSONField,
    ForeignKeyField,
    OnDelete,
)

from .base import DBBaseModel


class TaskStatus(StrEnum):
    """任务状态枚举"""

    PENDING = "pending"  # 等待执行
    RUNNING = "running"  # 正在执行
    SUCCESS = "success"  # 执行成功
    FAILED = "failed"  # 执行失败
    CANCELLED = "cancelled"  # 已取消
    PAUSED = "paused"  # 已暂停


class TaskType(StrEnum):
    """任务类型枚举"""

    CRON = "cron"  # 定时任务 (cron表达式)
    INTERVAL = "interval"  # 间隔任务 (固定间隔)
    DATE = "date"  # 一次性任务 (指定时间)
    MANUAL = "manual"  # 手动任务


class Task(DBBaseModel):
    """任务定义模型"""

    class Meta:
        table = "task"
        ordering = ["-id"]

    name = CharField(max_length=100, description="任务名称")
    description = TextField(null=True, description="任务描述")
    task_type = CharField(max_length=20, description="任务类型")

    # 任务执行配置
    module_path = CharField(max_length=255, description="任务模块路径")
    function_name = CharField(max_length=100, description="任务函数名")
    parameters = JSONField(default=dict, description="任务参数")

    # 调度配置
    schedule_config = JSONField(default=dict, description="调度配置")

    # 状态管理
    is_active = BooleanField(default=True, description="是否启用")

    # 执行统计
    total_runs = IntField(default=0, description="总执行次数")
    success_runs = IntField(default=0, description="成功执行次数")
    failed_runs = IntField(default=0, description="失败执行次数")

    # 时间记录
    last_run_at = DatetimeField(null=True, description="最后执行时间")
    next_run_at = DatetimeField(null=True, description="下次执行时间")

    def __str__(self):
        return f"Task({self.name})"


class TaskExecution(DBBaseModel):
    """任务执行记录模型"""

    class Meta:
        table = "task_execution"
        ordering = ["-id"]

    task = ForeignKeyField("models.Task", related_name="executions", on_delete=OnDelete.CASCADE, description="关联任务")
    task_id: int

    # 执行信息
    status = CharField(max_length=20, default=TaskStatus.PENDING.value, description="执行状态")
    trigger_type = CharField(max_length=20, description="触发类型")  # scheduled, manual, api

    # 执行时间
    started_at = DatetimeField(null=True, description="开始时间")
    finished_at = DatetimeField(null=True, description="结束时间")
    duration = IntField(null=True, description="执行时长(秒)")

    # 执行结果
    result = JSONField(null=True, description="执行结果")
    error_message = TextField(null=True, description="错误信息")
    traceback = TextField(null=True, description="错误堆栈")

    # 执行环境
    worker_id = CharField(max_length=100, null=True, description="执行器ID")
    parameters = JSONField(default=dict, description="执行参数")

    def __str__(self):
        return f"TaskExecution({self.task.name if self.task else 'Unknown'} - {self.status})"
