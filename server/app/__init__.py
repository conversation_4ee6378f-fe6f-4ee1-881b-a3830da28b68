import importlib
import sys
import time
from os import getenv

from asyncmy import connect
from loguru import logger

import traceback
from contextlib import asynccontextmanager

from starlette.requests import Request

from fastapi import APIRouter, FastAPI
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from tortoise.contrib.fastapi import RegisterTortoise
from tortoise.exceptions import DoesNotExist, IntegrityError
import pkgutil

from app.utils.patch import patch_all

patch_all()
logger.remove()
logger.add(sys.stderr, colorize=True)


async def wait_for_mysql():
    MYSQL_USER = getenv("MYSQL_USER", "zcws")
    MYSQL_PASSWORD = getenv("MYSQL_PASSWORD", "zcws")
    MYSQL_HOST = getenv("MYSQL_HOST", "db")
    MYSQL_DATABASE = getenv("MYSQL_DATABASE", "zcws")

    for _ in range(10):
        try:
            logger.info("尝试连接 MySQL...")
            await connect(
                user=MYSQL_USER,
                password=MYSQL_PASSWORD,
                host=MYSQL_HOST,
                database=MYSQL_DATABASE,
                charset="utf8mb4",
                auth_plugin_map={"caching_sha2_password": "caching_sha2_password"},
                connect_timeout=5,
            )
            logger.info("MySQL 连接成功!")
            break
        except Exception as e:
            logger.error(e)
            time.sleep(1)
    else:
        logger.critical(
            "无法连接到 MySQL，请检查配置和服务状态。请确保环境变量 MYSQL_USER, MYSQL_PASSWORD, MYSQL_HOST, MYSQL_DATABASE 正确设置。"
        )
        raise Exception


def get_router_from_module(module_name):
    """从模块中获取router"""
    module = importlib.import_module(module_name)
    if hasattr(module, "router") and isinstance(module.router, APIRouter):
        return module.router
    return None


def register_subpackages_to_parent(parent_router, package):
    """使用pkgutil递归注册子包的router到父包中"""
    # 使用pkgutil.iter_modules遍历包的直接子模块/子包
    for finder, name, ispkg in pkgutil.iter_modules(package.__path__, package.__name__ + "."):
        subpackage_name = name.split(".")[-1]  # 获取最后一级包名
        subrouter = get_router_from_module(name)
        if subrouter:
            if ispkg:
                # 导入子包以便进一步递归
                subpackage = importlib.import_module(name)
                # 先递归处理子包的子包
                register_subpackages_to_parent(subrouter, subpackage)
            # 将子包的router注册到父包中
            parent_router.include_router(subrouter, prefix=f"/{subpackage_name}")
        if not subrouter:
            # 如果模块中没有router，且不是包，则跳过
            if not ispkg:
                continue
            # 如果子包没有router，继续检查是否有更深层的子包
            subpackage = importlib.import_module(name)
            # 创建临时router来承载更深层的子包
            temp_router = APIRouter()
            register_subpackages_to_parent(temp_router, subpackage)

            if temp_router.routes:  # 如果临时router有路由，才注册
                parent_router.include_router(temp_router, prefix=f"/{subpackage_name}")


def standardize_header_key(key: str) -> str:
    """
    标准化 HTTP 请求头键，将每个连字符分隔的单词首字母大写
    """
    SPECIAL_CASES = {
        "dnt": "DNT",
        "etag": "ETag",
    }
    return "-".join(SPECIAL_CASES.get(word.lower(), word.capitalize()) for word in key.split("-"))


async def get_http_text(request: Request):
    body = await request.body()
    try:
        body = body.decode("utf-8")
    except Exception as e:
        body = str(body)

    return "\n".join(
        [
            f'{request.method.upper()} {request.url.path}{"?" if request.url.query else ""}{request.url.query} HTTP/1.1',
            *[f"{standardize_header_key(k)}: {v}" for k, v in request.headers.items()],
            "",
            body,
        ],
    )


def auto_register_routers_recursive(fastapi_app: FastAPI):
    """递归遍历api包，自动发现并注册所有子包的router"""

    api_module = importlib.import_module("app.api")
    main_api_router = get_router_from_module("app.api")
    if not main_api_router:
        raise Exception("主API路由未找到，请确保app/api/__init__.py中定义了router")

    logger.info("正在注册路由...")
    # 递归处理api包的所有子包
    register_subpackages_to_parent(main_api_router, api_module)

    # 注册主api router到FastAPI应用
    fastapi_app.include_router(main_api_router, prefix="/api")
    logger.info("路由注册完成")


def create_app(worker=True):
    from app import models, api
    from app.cache import cacheRedis
    from app.api.resp import MessageType, ResponseBase, assign_response
    from app.models import ExceptionLog
    from app.depends.real_ip import get_ip
    from app.depends.auth import oauth2_scheme, get_current_user
    from app.api.resp import NotificationType, ResponseType
    from app.static import TORTOISE_CONFIG

    @asynccontextmanager
    async def lifespan(app: FastAPI):
        await wait_for_mysql()

        logger.info("FastAPI应用启动中...")

        auto_register_routers_recursive(app)

        async with RegisterTortoise(
            app,
            config=TORTOISE_CONFIG,
            generate_schemas=True,
            # add_exception_handlers=True,
        ):
            if not worker:
                await cacheRedis.flushdb()
                await models.init()

            logger.info("FastAPI应用已启动")
            yield
        logger.info("FastAPI应用正在关闭...")

    fastapi_app = FastAPI(lifespan=lifespan)

    @assign_response(False, ResponseType.INTERNAL_SERVER_ERROR, "未知错误")
    class InternalServerErrorResp(ResponseBase): ...

    # 重写500错误
    @fastapi_app.exception_handler(Exception)
    async def exception_handler(request: Request, exc):
        ip = get_ip(request)
        user = None
        try:
            token = await oauth2_scheme(request)
            user = await get_current_user(token)
        except:
            pass

        exc_log = await ExceptionLog.create(
            user=user,
            ip=ip,
            method=request.method,
            path=request.url.path,
            payload=await get_http_text(request),
            traceback="".join(traceback.format_exception(exc)),
        )
        response = InternalServerErrorResp(desc=f"请联系管理员处理，请求ID: {exc_log.uuid}")
        return JSONResponse(response.model_dump())

    @assign_response(False, MessageType.ERROR, "主键冲突，请检查数据是否重复")
    class IntegrityErrorResp(ResponseBase): ...

    # 重写IntegrityError 主键冲突错误
    @fastapi_app.exception_handler(IntegrityError)
    async def integrity_exception_handler(request, exc):
        logger.error(request, exc)
        return JSONResponse(IntegrityErrorResp().model_dump())

    @assign_response(False, MessageType.INFO, "无数据")
    class DoesNotExistErrorResp(ResponseBase): ...

    # 重写查询数据库查询不存在
    @fastapi_app.exception_handler(DoesNotExist)
    async def not_exist_exception_handler(request, exc):
        logger.error(request, exc)
        return JSONResponse(DoesNotExistErrorResp().model_dump())

    # # 重写其他错误 返回200, 将http状态码放到code中
    # @fastapi_app.exception_handler(HTTPException)
    # async def http_exception_handler(request, exc):
    #     print("HTTPException", request, exc, flush=True)
    #     if type(exc.detail) is dict:
    #         return JSONResponse({"code": -exc.status_code, **exc.detail})
    #     return JSONResponse({"code": -exc.status_code, "msg": exc.detail})

    # 重写MessageBase
    @fastapi_app.exception_handler(ResponseBase)
    async def http_exception_handler(request, exc: ResponseBase):
        logger.error(("MessageBase", request, exc))
        return JSONResponse(exc.model_dump())

    # 重写模型表单校验错误
    @fastapi_app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request, exc):
        logger.error(("RequestValidationError", request, exc))
        return JSONResponse({"code": -400, "msg": "表单模型校验错误"})

    # # 重写模型表单校验错误
    # @fastapi_app.exception_handler(ResponseValidationError)
    # async def validation_exception_handler(request, exc):
    #     print(request, exc)
    #     return JSONResponse({
    #         'code': 400,
    #         'msg': "返回表单模型校验错误",
    #         'err': exc.errors()
    #     })

    return fastapi_app
