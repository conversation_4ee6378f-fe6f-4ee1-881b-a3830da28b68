"""
带状态管理的爬取示例

本模块展示如何使用状态管理功能实现增量爬取，适用于定时任务场景。
"""

import asyncio
from datetime import datetime, timedelta
from .models import CrawlConfig, ExtractionStrategy
from .article import crawl_articles, crawl_articles_with_pagination
from .state import generate_task_id, get_state_manager


async def example_incremental_crawl_by_time():
    """基于时间的增量爬取示例"""

    print("=== 基于时间的增量爬取示例 ===")

    # 自定义脚本，包含时间提取逻辑
    time_based_script = """
# 基于时间的增量爬取脚本

news_items = soup.select(".news-item, .article-item")

for item in news_items:
    try:
        # 提取标题
        title_element = item.select_one("h2, h3, .title")
        title = title_element.get_text().strip() if title_element else ""
        
        # 提取链接
        link_element = item.select_one("a")
        url = link_element.get("href", "") if link_element else ""
        if url and not url.startswith("http"):
            url = urljoin(response_data.get("url", ""), url)
        
        # 提取时间
        time_element = item.select_one(".time, .date, .publish-time")
        publish_time = ""
        if time_element:
            time_text = time_element.get_text().strip()
            # 尝试解析时间格式
            time_patterns = [
                r'(\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2})',
                r'(\\d{4}-\\d{2}-\\d{2})',
                r'(\\d{2}:\\d{2})'
            ]
            for pattern in time_patterns:
                match = re.search(pattern, time_text)
                if match:
                    publish_time = match.group(1)
                    break
        
        # 增量判断：如果启用状态管理，检查是否应该跳过
        should_skip = False
        if crawl_state and config.enable_state_management:
            if crawl_state.last_article_time and publish_time:
                try:
                    article_time = datetime.strptime(publish_time, config.time_format)
                    last_time = datetime.strptime(crawl_state.last_article_time, config.time_format)
                    should_skip = article_time <= last_time
                except:
                    pass
        
        if not should_skip and title and url:
            article = ArticleData(
                title=title,
                url=url,
                publish_time=publish_time,
                category="新闻"
            )
            articles.append(article)
            
    except Exception as e:
        print(f"提取文章时出错: {e}")
        continue
    """

    # 生成任务ID
    task_id = generate_task_id("https://example-news.com", "news_time")

    config = CrawlConfig(
        url="https://example-news.com/latest",
        extraction_strategy=ExtractionStrategy.CUSTOM_SCRIPT,
        custom_script=time_based_script,
        enable_state_management=True,
        task_id=task_id,
        incremental_strategy="time",
        time_format="%Y-%m-%d %H:%M:%S",
        auto_update_state=True,
        request_delay=2.0,
    )

    print(f"任务ID: {task_id}")
    print("首次爬取...")

    # 首次爬取
    result1 = await crawl_articles(config)
    if result1.success:
        print(f"✅ 首次爬取成功，提取到 {len(result1.articles)} 篇文章")
        for article in result1.articles[:3]:
            print(f"  - {article.title} ({article.publish_time})")

    print("\n等待5秒后进行增量爬取...")
    await asyncio.sleep(5)

    # 增量爬取
    result2 = await crawl_articles(config)
    if result2.success:
        print(f"✅ 增量爬取完成，提取到 {len(result2.articles)} 篇新文章")
        if result2.articles:
            for article in result2.articles[:3]:
                print(f"  - {article.title} ({article.publish_time})")
        else:
            print("  没有新文章")

    return result1, result2


async def example_incremental_crawl_by_title():
    """基于标题的增量爬取示例"""

    print("=== 基于标题的增量爬取示例 ===")

    title_based_script = """
# 基于标题的增量爬取脚本
article_items = soup.select(".article, .post, .news-item")

for item in article_items:
    try:
        # 提取标题
        title_element = item.select_one("h1, h2, h3, .title")
        title = title_element.get_text().strip() if title_element else ""
        
        # 提取链接
        link_element = item.select_one("a")
        url = link_element.get("href", "") if link_element else ""
        if url and not url.startswith("http"):
            url = urljoin(response_data.get("url", ""), url)
        
        # 增量判断：检查标题是否重复
        should_skip = False
        if crawl_state and config.enable_state_management:
            should_skip = (crawl_state.last_article_title == title)
        
        if not should_skip and title and url:
            article = ArticleData(
                title=title,
                url=url,
                category="博客"
            )
            articles.append(article)
            
    except Exception as e:
        continue
    """

    task_id = generate_task_id("https://example-blog.com", "blog_title")

    config = CrawlConfig(
        url="https://example-blog.com/posts",
        extraction_strategy=ExtractionStrategy.CUSTOM_SCRIPT,
        custom_script=title_based_script,
        enable_state_management=True,
        task_id=task_id,
        incremental_strategy="title",
        auto_update_state=True,
        stop_on_duplicate=True,
        duplicate_threshold=2,
    )

    print(f"任务ID: {task_id}")
    result = await crawl_articles(config)

    if result.success:
        print(f"✅ 基于标题的增量爬取成功，提取到 {len(result.articles)} 篇文章")
    else:
        print(f"❌ 爬取失败: {result.error_message}")

    return result


async def example_stateful_pagination():
    """带状态管理的分页爬取示例"""

    print("=== 带状态管理的分页爬取示例 ===")

    pagination_script = """
# 分页爬取脚本，支持状态管理
article_items = soup.select(".article-item, .news-item")

for item in article_items:
    try:
        title_element = item.select_one("h2, .title")
        title = title_element.get_text().strip() if title_element else ""
        
        link_element = item.select_one("a")
        url = link_element.get("href", "") if link_element else ""
        if url and not url.startswith("http"):
            url = urljoin(response_data.get("url", ""), url)
        
        # 提取时间
        time_element = item.select_one(".date, .time")
        publish_time = time_element.get_text().strip() if time_element else ""
        
        if title and url:
            article = ArticleData(
                title=title,
                url=url,
                publish_time=publish_time
            )
            articles.append(article)
            
    except:
        continue

# 提取分页信息
try:
    next_link = soup.select_one(".pagination .next, a:contains('下一页')")
    if next_link:
        next_url = next_link.get("href", "")
        if next_url and not next_url.startswith("http"):
            next_url = urljoin(response_data.get("url", ""), next_url)
        
        pagination = PaginationInfo(
            has_next=True,
            next_page_url=next_url
        )
except:
    pass
    """

    task_id = generate_task_id("https://example-news.com/archive", "news_pagination")

    config = CrawlConfig(
        url="https://example-news.com/archive/page/1",
        extraction_strategy=ExtractionStrategy.CUSTOM_SCRIPT,
        custom_script=pagination_script,
        enable_pagination=True,
        max_pages=3,
        enable_state_management=True,
        task_id=task_id,
        incremental_strategy="time",
        auto_update_state=True,
        max_incremental_pages=3,
        stop_on_duplicate=True,
        request_delay=2.0,
    )

    print(f"任务ID: {task_id}")
    result = await crawl_articles_with_pagination(config)

    if result.success:
        print(f"✅ 分页爬取成功，共爬取 {result.total_pages_crawled} 页，提取到 {result.total_articles} 篇文章")
    else:
        print(f"❌ 分页爬取失败: {result.error_message}")

    return result


async def example_state_management():
    """状态管理功能演示"""

    print("=== 状态管理功能演示 ===")

    state_manager = get_state_manager()

    # 创建测试任务
    task_id = "test_task_" + str(int(datetime.now().timestamp()))
    url = "https://example.com/test"

    # 创建状态
    state = await state_manager.get_or_create_state(task_id, url)
    print(f"✅ 创建状态: {task_id}")
    print(f"   URL: {state.url}")
    print(f"   创建时间: {state.created_time}")

    # 更新状态
    await state_manager.update_state(task_id, total_articles=10, last_article_title="测试文章标题", last_page=2)
    print("✅ 状态已更新")

    # 重新加载状态
    updated_state = await state_manager.load_state(task_id)
    if updated_state:
        print(f"✅ 重新加载状态成功")
        print(f"   总文章数: {updated_state.total_articles}")
        print(f"   最后文章标题: {updated_state.last_article_title}")
        print(f"   最后页码: {updated_state.last_page}")

    # 列出所有状态
    all_states = await state_manager.list_states(limit=5)
    print(f"✅ 当前共有 {len(all_states)} 个状态")

    # 清理测试状态
    await state_manager.delete_state(task_id)
    print("✅ 测试状态已清理")


async def run_stateful_examples():
    """运行所有带状态管理的示例"""

    print("🚀 开始运行带状态管理的爬取示例...")
    print("=" * 60)

    examples = [
        ("状态管理功能演示", example_state_management),
        ("基于时间的增量爬取", example_incremental_crawl_by_time),
        ("基于标题的增量爬取", example_incremental_crawl_by_title),
        ("带状态管理的分页爬取", example_stateful_pagination),
    ]

    for name, example_func in examples:
        try:
            print(f"\n🔄 运行示例: {name}")
            await example_func()
        except Exception as e:
            print(f"❌ 示例 {name} 运行失败: {str(e)}")

        print("-" * 40)

    print("\n✨ 所有状态管理示例运行完成！")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(run_stateful_examples())
