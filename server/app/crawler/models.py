"""
网页文章爬取相关数据模型

本模块定义了爬取过程中使用的所有数据结构，包括：
- 爬取配置模型
- 文章数据模型
- 分页信息模型
- 脚本执行结果模型
"""

from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, field_validator
from enum import Enum
import json


class CrawlMethod(str, Enum):
    """爬取方法枚举"""

    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"


class ResponseFormat(str, Enum):
    """响应格式枚举"""

    HTML = "html"
    JSON = "json"
    XML = "xml"
    TEXT = "text"


class ExtractionStrategy(str, Enum):
    """数据提取策略枚举"""

    CUSTOM_SCRIPT = "custom_script"  # 自定义脚本
    CSS_SELECTOR = "css_selector"  # CSS选择器
    REGEX = "regex"  # 正则表达式
    LLM = "llm"  # LLM提取


class ArticleData(BaseModel):
    """文章数据模型"""

    title: str = Field(..., description="文章标题")
    content: Optional[str] = Field(None, description="文章内容")
    summary: Optional[str] = Field(None, description="文章摘要")
    author: Optional[str] = Field(None, description="作者")
    publish_time: Optional[str] = Field(None, description="发布时间")
    url: str = Field(..., description="文章链接")
    category: Optional[str] = Field(None, description="分类")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签列表")
    image_url: Optional[str] = Field(None, description="封面图片URL")
    read_count: Optional[int] = Field(None, description="阅读数")
    like_count: Optional[int] = Field(None, description="点赞数")
    comment_count: Optional[int] = Field(None, description="评论数")
    extra_data: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外数据")


class PaginationInfo(BaseModel):
    """分页信息模型"""

    current_page: int = Field(1, description="当前页码")
    total_pages: Optional[int] = Field(None, description="总页数")
    page_size: int = Field(20, description="每页大小")
    total_items: Optional[int] = Field(None, description="总条目数")
    has_next: bool = Field(False, description="是否有下一页")
    next_page_url: Optional[str] = Field(None, description="下一页URL")
    next_page_params: Optional[Dict[str, Any]] = Field(None, description="下一页参数")


class CrawlConfig(BaseModel):
    """爬取配置模型"""

    # 基础配置
    url: str = Field(..., description="目标URL")
    method: CrawlMethod = Field(CrawlMethod.GET, description="HTTP方法")
    headers: Optional[Dict[str, str]] = Field(default_factory=dict, description="请求头")
    params: Optional[Dict[str, Any]] = Field(default_factory=dict, description="请求参数")
    data: Optional[Dict[str, Any]] = Field(default_factory=dict, description="POST数据")
    cookies: Optional[Dict[str, str]] = Field(default_factory=dict, description="Cookie")

    # 爬取策略配置
    extraction_strategy: ExtractionStrategy = Field(ExtractionStrategy.CUSTOM_SCRIPT, description="提取策略")
    custom_script: Optional[str] = Field(None, description="自定义Python脚本")
    css_selectors: Optional[Dict[str, str]] = Field(default_factory=dict, description="CSS选择器映射")
    regex_patterns: Optional[Dict[str, str]] = Field(default_factory=dict, description="正则表达式映射")

    # 分页配置
    enable_pagination: bool = Field(False, description="是否启用分页")
    max_pages: int = Field(10, description="最大页数")
    pagination_script: Optional[str] = Field(None, description="分页处理脚本")

    # 反爬虫配置
    user_agent: Optional[str] = Field(None, description="User-Agent")
    request_delay: float = Field(1.0, description="请求间隔(秒)")
    random_delay: bool = Field(True, description="是否随机延迟")
    use_proxy: bool = Field(False, description="是否使用代理")
    proxy_list: Optional[List[str]] = Field(default_factory=list, description="代理列表")

    # 超时配置
    timeout: int = Field(30, description="请求超时时间(秒)")
    retry_times: int = Field(3, description="重试次数")

    # 浏览器配置
    use_browser: bool = Field(False, description="是否使用浏览器渲染")
    headless: bool = Field(True, description="是否无头模式")
    wait_for_selector: Optional[str] = Field(None, description="等待元素选择器")
    wait_time: int = Field(5, description="等待时间(秒)")

    @field_validator("custom_script")
    def validate_custom_script(cls, v):
        """验证自定义脚本的安全性"""
        if v:
            # 检查危险函数调用
            dangerous_keywords = [
                "import os",
                "import sys",
                "import subprocess",
                "exec(",
                "eval(",
                "__import__",
                "open(",
                "file(",
                "input(",
                "raw_input(",
            ]
            for keyword in dangerous_keywords:
                if keyword in v:
                    raise ValueError(f"脚本包含危险关键字: {keyword}")
        return v


class ScriptExecutionResult(BaseModel):
    """脚本执行结果模型"""

    success: bool = Field(..., description="执行是否成功")
    data: Optional[List[ArticleData]] = Field(default_factory=list, description="提取的文章数据")
    pagination: Optional[PaginationInfo] = Field(None, description="分页信息")
    error_message: Optional[str] = Field(None, description="错误信息")
    execution_time: float = Field(0.0, description="执行时间(秒)")
    extracted_count: int = Field(0, description="提取的文章数量")
    raw_data: Optional[Dict[str, Any]] = Field(None, description="原始数据")


class CrawlResult(BaseModel):
    """爬取结果模型"""

    success: bool = Field(..., description="爬取是否成功")
    articles: List[ArticleData] = Field(default_factory=list, description="文章列表")
    pagination: Optional[PaginationInfo] = Field(None, description="分页信息")
    total_pages_crawled: int = Field(0, description="已爬取页数")
    total_articles: int = Field(0, description="总文章数")
    error_message: Optional[str] = Field(None, description="错误信息")
    execution_time: float = Field(0.0, description="总执行时间(秒)")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="元数据")

    def add_articles(self, articles: List[ArticleData]):
        """添加文章数据"""
        self.articles.extend(articles)
        self.total_articles = len(self.articles)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "success": self.success,
            "articles": [article.model_dump() for article in self.articles],
            "pagination": self.pagination.model_dump() if self.pagination else None,
            "total_pages_crawled": self.total_pages_crawled,
            "total_articles": self.total_articles,
            "error_message": self.error_message,
            "execution_time": self.execution_time,
            "metadata": self.metadata,
        }
