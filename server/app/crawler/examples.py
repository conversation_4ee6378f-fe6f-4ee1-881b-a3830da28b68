"""
网页文章爬取示例和测试用例

本模块提供各种类型网站的爬取示例，帮助用户快速上手使用通用爬取功能。
包含以下示例：
1. 新闻网站爬取示例
2. 博客网站爬取示例
3. API接口爬取示例
4. 分页爬取示例
5. 批量爬取示例
"""

import asyncio
from typing import List
from .models import CrawlConfig, ExtractionStrategy, CrawlMethod
from .article import crawl_articles, crawl_articles_with_pagination
from .templates import get_template


# 示例1: 新闻网站爬取
async def example_news_website():
    """新闻网站爬取示例 - 金色财经快讯"""

    print("=== 新闻网站爬取示例 ===")

    # 自定义脚本，适配金色财经快讯页面
    custom_script = """
# 金色财经快讯页面文章提取脚本
news_items = soup.select(".live-item, .news-item")

for item in news_items:
    try:
        # 提取标题
        title_element = item.select_one(".live-title, .title, h3, h2")
        title = title_element.get_text().strip() if title_element else ""
        
        # 提取链接
        link_element = item.select_one("a")
        url = link_element.get("href", "") if link_element else ""
        if url and not url.startswith("http"):
            url = urljoin(response_data.get("url", ""), url)
        
        # 提取内容
        content_element = item.select_one(".live-content, .content, .summary")
        content = content_element.get_text().strip() if content_element else ""
        
        # 提取时间
        time_element = item.select_one(".live-time, .time, .date")
        publish_time = time_element.get_text().strip() if time_element else ""
        
        # 创建文章对象
        if title:
            article = ArticleData(
                title=title,
                url=url or response_data.get("url", ""),
                content=content,
                publish_time=publish_time,
                category="快讯"
            )
            articles.append(article)
            
    except Exception as e:
        print(f"提取文章时出错: {e}")
        continue
    """

    config = CrawlConfig(
        url="https://api.jinse.cn/noah/v2/lives?limit=20",
        extraction_strategy=ExtractionStrategy.CUSTOM_SCRIPT,
        custom_script=custom_script,
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        request_delay=2.0,
        timeout=30,
    )

    result = await crawl_articles(config)

    if result.success:
        print(f"✅ 爬取成功！提取到 {len(result.articles)} 篇文章")
        for i, article in enumerate(result.articles[:3], 1):
            print(f"{i}. {article.title}")
            print(f"   链接: {article.url}")
            print(f"   时间: {article.publish_time}")
            print()
    else:
        print(f"❌ 爬取失败: {result.error_message}")

    return result


# 示例2: 博客网站爬取
async def example_blog_website():
    """博客网站爬取示例"""

    print("=== 博客网站爬取示例 ===")

    # 使用博客模板
    blog_script = get_template("blog")

    config = CrawlConfig(
        url="https://example-blog.com",  # 替换为实际的博客URL
        extraction_strategy=ExtractionStrategy.CUSTOM_SCRIPT,
        custom_script=blog_script,
        request_delay=1.5,
    )

    result = await crawl_articles(config)

    if result.success:
        print(f"✅ 博客爬取成功！提取到 {len(result.articles)} 篇文章")
        for article in result.articles[:2]:
            print(f"- {article.title}")
            print(f"  作者: {article.author}")
            print(f"  分类: {article.category}")
            print()
    else:
        print(f"❌ 博客爬取失败: {result.error_message}")

    return result


# 示例3: API接口爬取
async def example_api_crawl():
    """API接口爬取示例"""

    print("=== API接口爬取示例 ===")

    # API接口脚本模板
    # api_script = get_template("api")
    api_script = '''
    """
    API接口数据提取脚本

    适用于返回JSON格式数据的API接口
    """

    try:
        # 解析JSON数据
        if isinstance(response_data.get("content"), str):
            data = json.loads(response_data["content"])
        else:
            data = response_data.get("content", {})
        print(data, flush=True)
        exit()
        # 根据API响应结构提取文章列表
        # 常见的数据结构路径
        articles_data = None
        possible_paths = [
            data.get("data", {}).get("list", []),
            data.get("data", []),
            data.get("list", []),
            data.get("items", []),
            data.get("articles", []),
            data.get("results", [])
        ]

        for path in possible_paths:
            if isinstance(path, list) and path:
                articles_data = path
                break

        if not articles_data:
            articles_data = data if isinstance(data, list) else []

        # 提取每篇文章的信息
        for item in articles_data:
            try:
                # 根据API字段映射提取信息
                title = item.get("title", "") or item.get("name", "") or item.get("subject", "")
                url = item.get("url", "") or item.get("link", "") or item.get("href", "")
                content = item.get("content", "") or item.get("summary", "") or item.get("description", "")
                author = item.get("author", "") or item.get("writer", "") or item.get("user", {}).get("name", "")
                publish_time = item.get("publish_time", "") or item.get("created_at", "") or item.get("time", "")
                category = item.get("category", "") or item.get("type", "") or item.get("section", "")

                # 处理嵌套结构
                if isinstance(author, dict):
                    author = author.get("name", "") or author.get("username", "")

                # 提取数字统计
                read_count = item.get("read_count", 0) or item.get("views", 0) or item.get("pv", 0)
                like_count = item.get("like_count", 0) or item.get("likes", 0) or item.get("praise", 0)
                comment_count = item.get("comment_count", 0) or item.get("comments", 0) or item.get("reply", 0)

                # 创建文章对象
                if title:
                    article = ArticleData(
                        title=title,
                        url=url,
                        content=content,
                        author=author,
                        publish_time=publish_time,
                        category=category,
                        read_count=read_count,
                        like_count=like_count,
                        comment_count=comment_count
                    )
                    articles.append(article)

            except Exception as e:
                print(f"提取API文章时出错: {e}")
                continue

        # 提取分页信息
        try:
            pagination_data = data.get("pagination", {}) or data.get("page", {})
            if pagination_data:
                current_page = pagination_data.get("current", 1) or pagination_data.get("page", 1)
                total_pages = pagination_data.get("total", 1) or pagination_data.get("pages", 1)
                has_next = pagination_data.get("has_next", False) or current_page < total_pages

                pagination = PaginationInfo(
                    current_page=current_page,
                    total_pages=total_pages,
                    has_next=has_next
                )
        except:
            pass

    except Exception as e:
        print(f"解析API数据时出错: {e}")
    '''

    config = CrawlConfig(
        url="https://api.jinse.cn/noah/v2/lives?limit=20&reading=false&source=web&flag=down&id=469437&category=0",  # 替换为实际的API URL
        method=CrawlMethod.GET,
        extraction_strategy=ExtractionStrategy.CUSTOM_SCRIPT,
        custom_script=api_script,
        headers={"Accept": "application/json", "Content-Type": "application/json"},
        params={"page": 1, "limit": 20},
    )

    result = await crawl_articles(config)

    if result.success:
        print(f"✅ API爬取成功！提取到 {len(result.articles)} 篇文章")
    else:
        print(f"❌ API爬取失败: {result.error_message}")

    return result


# 示例4: 分页爬取
async def example_pagination_crawl():
    """分页爬取示例"""

    print("=== 分页爬取示例 ===")

    # 分页脚本
    pagination_script = """
# 分页新闻爬取脚本
article_items = soup.select(".article-item, .news-item, .post")

for item in article_items:
    try:
        title_element = item.select_one("h2, h3, .title")
        title = title_element.get_text().strip() if title_element else ""
        
        link_element = item.select_one("a")
        url = link_element.get("href", "") if link_element else ""
        if url and not url.startswith("http"):
            url = urljoin(response_data.get("url", ""), url)
        
        if title and url:
            article = ArticleData(title=title, url=url)
            articles.append(article)
    except:
        continue

# 提取分页信息
try:
    next_link = soup.select_one(".pagination .next, .pager .next, a:contains('下一页')")
    if next_link:
        next_url = next_link.get("href", "")
        if next_url and not next_url.startswith("http"):
            next_url = urljoin(response_data.get("url", ""), next_url)
        
        pagination = PaginationInfo(
            has_next=True,
            next_page_url=next_url
        )
except:
    pass
    """

    config = CrawlConfig(
        url="https://example-news.com/page/1",  # 替换为实际的分页URL
        extraction_strategy=ExtractionStrategy.CUSTOM_SCRIPT,
        custom_script=pagination_script,
        enable_pagination=True,
        max_pages=3,  # 最多爬取3页
        request_delay=2.0,
    )

    result = await crawl_articles_with_pagination(config)

    if result.success:
        print(f"✅ 分页爬取成功！共爬取 {result.total_pages_crawled} 页，提取到 {result.total_articles} 篇文章")
    else:
        print(f"❌ 分页爬取失败: {result.error_message}")

    return result


# 示例6: CSS选择器爬取
async def example_css_selector_crawl():
    """CSS选择器爬取示例"""

    print("=== CSS选择器爬取示例 ===")

    config = CrawlConfig(
        url="https://example.com/articles",
        extraction_strategy=ExtractionStrategy.CSS_SELECTOR,
        css_selectors={
            "container": ".article-list .article-item",  # 文章容器选择器
            "title": "h2.title, .article-title",  # 标题选择器
            "link": "a.article-link",  # 链接选择器
            "content": ".article-summary, .excerpt",  # 内容选择器
            "author": ".author-name",  # 作者选择器
            "time": ".publish-date, .article-date",  # 时间选择器
            "category": ".category, .tag",  # 分类选择器
        },
        request_delay=1.0,
    )

    result = await crawl_articles(config)

    if result.success:
        print(f"✅ CSS选择器爬取成功！提取到 {len(result.articles)} 篇文章")
        for article in result.articles[:2]:
            print(f"- {article.title}")
            print(f"  作者: {article.author}")
            print(f"  时间: {article.publish_time}")
            print()
    else:
        print(f"❌ CSS选择器爬取失败: {result.error_message}")

    return result


# 运行所有示例
async def run_all_examples():
    """运行所有示例"""
    print("🚀 开始运行网页文章爬取示例...")
    print("=" * 50)

    examples = [
        # ("新闻网站爬取", example_news_website),
        # ("CSS选择器爬取", example_css_selector_crawl),
        # ("博客网站爬取", example_blog_website),
        ("API接口爬取", example_api_crawl),
        # ("分页爬取", example_pagination_crawl),
    ]

    for name, example_func in examples:
        try:
            print(f"\n🔄 运行示例: {name}")
            await example_func()
        except Exception as e:
            print(f"❌ 示例 {name} 运行失败: {str(e)}")

        print("-" * 30)

    print("\n✨ 所有示例运行完成！")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(run_all_examples())
