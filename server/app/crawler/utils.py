"""
网页文章爬取工具函数

本模块提供爬取过程中需要的各种工具函数，包括：
- 安全脚本执行环境
- HTTP请求工具
- 数据清洗和验证
- 反爬虫处理
"""

import re
import time
import random
import asyncio
import hashlib
from typing import Dict, Any, List, Optional, Union
from urllib.parse import urljoin, urlparse, parse_qs, urlencode
import json
from datetime import datetime
import logging

import requests
from bs4 import BeautifulSoup
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, BrowserConfig
from crawl4ai import Crawl4aiDockerClient

from .models import ArticleData, PaginationInfo, ScriptExecutionResult, CrawlConfig

# 配置日志
logger = logging.getLogger(__name__)


class SafeScriptExecutor:
    """安全的脚本执行器"""
    
    def __init__(self):
        self.allowed_modules = {
            're', 'json', 'datetime', 'time', 'random', 'math',
            'urllib.parse', 'base64', 'hashlib'
        }
        self.forbidden_functions = {
            'exec', 'eval', 'compile', '__import__', 'open', 'file',
            'input', 'raw_input', 'reload', 'vars', 'globals', 'locals',
            'dir', 'getattr', 'setattr', 'delattr', 'hasattr'
        }
    
    def create_safe_globals(self, soup: BeautifulSoup, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建安全的全局变量环境"""
        safe_globals = {
            '__builtins__': {
                'len': len, 'str': str, 'int': int, 'float': float, 'bool': bool,
                'list': list, 'dict': dict, 'tuple': tuple, 'set': set,
                'min': min, 'max': max, 'sum': sum, 'abs': abs,
                'round': round, 'sorted': sorted, 'reversed': reversed,
                'enumerate': enumerate, 'zip': zip, 'range': range,
                'isinstance': isinstance, 'type': type, 'print': print
            },
            # 提供给脚本使用的变量
            'soup': soup,
            'response_data': response_data,
            'articles': [],  # 用于存储提取的文章
            'pagination': None,  # 用于存储分页信息
            'ArticleData': ArticleData,
            'PaginationInfo': PaginationInfo,
            # 工具函数
            'urljoin': urljoin,
            'urlparse': urlparse,
            'parse_qs': parse_qs,
            'urlencode': urlencode,
            're': re,
            'json': json,
            'datetime': datetime,
            'time': time,
            'random': random,
        }
        return safe_globals
    
    def validate_script(self, script: str) -> bool:
        """验证脚本安全性"""
        # 检查禁用函数
        for func in self.forbidden_functions:
            if func in script:
                raise ValueError(f"脚本包含禁用函数: {func}")
        
        # 检查危险导入
        import_pattern = r'import\s+(\w+)'
        imports = re.findall(import_pattern, script)
        for module in imports:
            if module not in self.allowed_modules:
                raise ValueError(f"不允许导入模块: {module}")
        
        return True
    
    def execute_script(self, script: str, soup: BeautifulSoup, response_data: Dict[str, Any]) -> ScriptExecutionResult:
        """安全执行脚本"""
        start_time = time.time()
        
        try:
            # 验证脚本安全性
            self.validate_script(script)
            
            # 创建安全执行环境
            safe_globals = self.create_safe_globals(soup, response_data)
            
            # 执行脚本
            exec(script, safe_globals)
            
            # 提取结果
            articles_data = safe_globals.get('articles', [])
            pagination_data = safe_globals.get('pagination', None)
            
            # 转换为模型对象
            articles = []
            for article_dict in articles_data:
                if isinstance(article_dict, dict):
                    articles.append(ArticleData(**article_dict))
                elif isinstance(article_dict, ArticleData):
                    articles.append(article_dict)
            
            pagination = None
            if pagination_data:
                if isinstance(pagination_data, dict):
                    pagination = PaginationInfo(**pagination_data)
                elif isinstance(pagination_data, PaginationInfo):
                    pagination = pagination_data
            
            execution_time = time.time() - start_time
            
            return ScriptExecutionResult(
                success=True,
                data=articles,
                pagination=pagination,
                execution_time=execution_time,
                extracted_count=len(articles)
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"脚本执行失败: {str(e)}")
            return ScriptExecutionResult(
                success=False,
                error_message=str(e),
                execution_time=execution_time,
                extracted_count=0
            )


class AntiCrawlerHandler:
    """反爬虫处理器"""
    
    def __init__(self):
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
        ]
    
    def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        return random.choice(self.user_agents)
    
    def get_random_delay(self, base_delay: float = 1.0, variance: float = 0.5) -> float:
        """获取随机延迟时间"""
        return base_delay + random.uniform(-variance, variance)
    
    def prepare_headers(self, config: CrawlConfig) -> Dict[str, str]:
        """准备请求头"""
        headers = config.headers.copy() if config.headers else {}
        
        # 设置User-Agent
        if not headers.get('User-Agent'):
            headers['User-Agent'] = config.user_agent or self.get_random_user_agent()
        
        # 设置常见请求头
        default_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        for key, value in default_headers.items():
            if key not in headers:
                headers[key] = value
        
        return headers
    
    async def apply_delay(self, config: CrawlConfig):
        """应用延迟"""
        if config.random_delay:
            delay = self.get_random_delay(config.request_delay)
        else:
            delay = config.request_delay
        
        if delay > 0:
            await asyncio.sleep(delay)


class DataCleaner:
    """数据清洗器"""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """清洗文本数据"""
        if not text:
            return ""
        
        # 移除多余空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 移除特殊字符
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)
        
        return text.strip()
    
    @staticmethod
    def clean_url(url: str, base_url: str = None) -> str:
        """清洗URL"""
        if not url:
            return ""
        
        url = url.strip()
        
        # 处理相对URL
        if base_url and not url.startswith(('http://', 'https://')):
            url = urljoin(base_url, url)
        
        return url
    
    @staticmethod
    def parse_number(text: str) -> Optional[int]:
        """解析数字"""
        if not text:
            return None
        
        # 移除非数字字符
        numbers = re.findall(r'\d+', str(text))
        if numbers:
            return int(''.join(numbers))
        
        return None
    
    @staticmethod
    def validate_article_data(article: Dict[str, Any]) -> bool:
        """验证文章数据完整性"""
        required_fields = ['title', 'url']
        
        for field in required_fields:
            if not article.get(field):
                return False
        
        return True


class URLBuilder:
    """URL构建器"""
    
    @staticmethod
    def build_paginated_url(base_url: str, page: int, params: Dict[str, Any] = None) -> str:
        """构建分页URL"""
        parsed = urlparse(base_url)
        query_params = parse_qs(parsed.query)
        
        # 添加页码参数
        if params:
            query_params.update(params)
        
        # 常见的页码参数名
        page_param_names = ['page', 'p', 'pageNum', 'pageNo', 'offset']
        
        # 尝试找到现有的页码参数
        page_param = None
        for param_name in page_param_names:
            if param_name in query_params:
                page_param = param_name
                break
        
        # 如果没有找到，使用默认的'page'
        if not page_param:
            page_param = 'page'
        
        query_params[page_param] = [str(page)]
        
        # 重建URL
        new_query = urlencode(query_params, doseq=True)
        new_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
        if new_query:
            new_url += f"?{new_query}"
        
        return new_url
    
    @staticmethod
    def extract_pagination_info(soup: BeautifulSoup, current_url: str) -> Optional[PaginationInfo]:
        """从页面中提取分页信息"""
        try:
            # 查找分页元素的常见选择器
            pagination_selectors = [
                '.pagination', '.pager', '.page-nav', '.page-list',
                '[class*="page"]', '[class*="paging"]'
            ]
            
            pagination_element = None
            for selector in pagination_selectors:
                element = soup.select_one(selector)
                if element:
                    pagination_element = element
                    break
            
            if not pagination_element:
                return None
            
            # 提取页码信息
            page_links = pagination_element.find_all('a')
            current_page = 1
            total_pages = 1
            has_next = False
            next_page_url = None
            
            # 尝试从链接中提取信息
            for link in page_links:
                href = link.get('href', '')
                text = link.get_text().strip()
                
                # 查找"下一页"链接
                if any(keyword in text.lower() for keyword in ['next', '下一页', '下页', '>']):
                    has_next = True
                    next_page_url = urljoin(current_url, href)
                
                # 尝试提取页码
                if text.isdigit():
                    page_num = int(text)
                    total_pages = max(total_pages, page_num)
            
            return PaginationInfo(
                current_page=current_page,
                total_pages=total_pages,
                has_next=has_next,
                next_page_url=next_page_url
            )
            
        except Exception as e:
            logger.warning(f"提取分页信息失败: {str(e)}")
            return None


def generate_request_id() -> str:
    """生成请求ID"""
    timestamp = str(int(time.time() * 1000))
    random_str = str(random.randint(1000, 9999))
    return hashlib.md5(f"{timestamp}{random_str}".encode()).hexdigest()[:16]


def format_execution_time(seconds: float) -> str:
    """格式化执行时间"""
    if seconds < 1:
        return f"{seconds*1000:.0f}ms"
    elif seconds < 60:
        return f"{seconds:.2f}s"
    else:
        minutes = int(seconds // 60)
        remaining_seconds = seconds % 60
        return f"{minutes}m{remaining_seconds:.2f}s"
