"""
网页文章爬取脚本模板和示例

本模块提供各种类型网站的爬取脚本模板和示例，用户可以基于这些模板
快速编写适配不同网站的爬取脚本。

脚本执行环境中可用的变量和函数：
- soup: BeautifulSoup对象，用于HTML解析
- response_data: 响应数据字典
- articles: 文章列表，脚本需要将提取的文章添加到此列表
- pagination: 分页信息，脚本可以设置此变量
- ArticleData: 文章数据模型类
- PaginationInfo: 分页信息模型类
- 各种工具函数: urljoin, urlparse, re, json, datetime等
"""

# 基础脚本模板
BASIC_TEMPLATE = '''
"""
基础文章提取脚本模板

使用说明：
1. 使用soup对象解析HTML内容
2. 提取文章信息并创建ArticleData对象
3. 将文章添加到articles列表中
4. 如需分页，设置pagination变量
"""

# 查找文章容器
article_containers = soup.select("文章容器选择器")

for container in article_containers:
    try:
        # 提取标题
        title_element = container.select_one("标题选择器")
        title = title_element.get_text().strip() if title_element else ""
        
        # 提取链接
        link_element = container.select_one("链接选择器")
        url = link_element.get("href", "") if link_element else ""
        if url and not url.startswith("http"):
            url = urljoin(response_data.get("url", ""), url)
        
        # 提取内容/摘要
        content_element = container.select_one("内容选择器")
        content = content_element.get_text().strip() if content_element else ""
        
        # 提取时间
        time_element = container.select_one("时间选择器")
        publish_time = time_element.get_text().strip() if time_element else ""
        
        # 提取作者
        author_element = container.select_one("作者选择器")
        author = author_element.get_text().strip() if author_element else ""
        
        # 创建文章对象
        if title and url:
            article = ArticleData(
                title=title,
                url=url,
                content=content,
                author=author,
                publish_time=publish_time
            )
            articles.append(article)
            
    except Exception as e:
        print(f"提取文章时出错: {e}")
        continue

# 提取分页信息（可选）
try:
    # 查找下一页链接
    next_link = soup.select_one("下一页选择器")
    if next_link:
        next_url = next_link.get("href", "")
        if next_url and not next_url.startswith("http"):
            next_url = urljoin(response_data.get("url", ""), next_url)
        
        pagination = PaginationInfo(
            has_next=True,
            next_page_url=next_url
        )
except:
    pass
'''

# 新闻网站模板
NEWS_WEBSITE_TEMPLATE = '''
"""
新闻网站文章提取脚本

适用于大多数新闻网站的文章列表页面
"""

# 查找新闻文章列表
news_items = soup.select("div.news-item, .article-item, .news-list li, .content-item")

for item in news_items:
    try:
        # 提取标题
        title_selectors = ["h1", "h2", "h3", ".title", ".headline", "a[title]"]
        title = ""
        title_element = None
        
        for selector in title_selectors:
            title_element = item.select_one(selector)
            if title_element:
                title = title_element.get_text().strip()
                break
        
        if not title and title_element:
            title = title_element.get("title", "").strip()
        
        # 提取链接
        link_element = title_element or item.select_one("a")
        url = ""
        if link_element:
            url = link_element.get("href", "")
            if url and not url.startswith("http"):
                url = urljoin(response_data.get("url", ""), url)
        
        # 提取摘要
        summary_selectors = [".summary", ".excerpt", ".description", ".content", "p"]
        summary = ""
        for selector in summary_selectors:
            summary_element = item.select_one(selector)
            if summary_element:
                summary = summary_element.get_text().strip()[:200]
                break
        
        # 提取发布时间
        time_selectors = [".time", ".date", ".publish-time", "[datetime]", "time"]
        publish_time = ""
        for selector in time_selectors:
            time_element = item.select_one(selector)
            if time_element:
                publish_time = time_element.get_text().strip()
                if not publish_time:
                    publish_time = time_element.get("datetime", "")
                break
        
        # 提取作者
        author_selectors = [".author", ".writer", ".by", ".source"]
        author = ""
        for selector in author_selectors:
            author_element = item.select_one(selector)
            if author_element:
                author = author_element.get_text().strip()
                break
        
        # 提取分类
        category_selectors = [".category", ".tag", ".section", ".channel"]
        category = ""
        for selector in category_selectors:
            category_element = item.select_one(selector)
            if category_element:
                category = category_element.get_text().strip()
                break
        
        # 提取图片
        img_element = item.select_one("img")
        image_url = ""
        if img_element:
            image_url = img_element.get("src", "") or img_element.get("data-src", "")
            if image_url and not image_url.startswith("http"):
                image_url = urljoin(response_data.get("url", ""), image_url)
        
        # 创建文章对象
        if title and url:
            article = ArticleData(
                title=title,
                url=url,
                summary=summary,
                author=author,
                publish_time=publish_time,
                category=category,
                image_url=image_url
            )
            articles.append(article)
            
    except Exception as e:
        print(f"提取新闻文章时出错: {e}")
        continue

# 提取分页信息
try:
    # 查找分页容器
    pagination_container = soup.select_one(".pagination, .pager, .page-nav")
    if pagination_container:
        # 查找下一页链接
        next_selectors = ["a:contains('下一页'), a:contains('下页'), a:contains('Next'), a:contains('>')"]
        next_link = None
        
        for selector in next_selectors:
            next_link = pagination_container.select_one(selector)
            if next_link:
                break
        
        if next_link:
            next_url = next_link.get("href", "")
            if next_url and not next_url.startswith("http"):
                next_url = urljoin(response_data.get("url", ""), next_url)
            
            pagination = PaginationInfo(
                has_next=True,
                next_page_url=next_url
            )
except:
    pass
'''

# 博客网站模板
BLOG_TEMPLATE = '''
"""
博客网站文章提取脚本

适用于个人博客或博客平台
"""

# 查找博客文章
blog_posts = soup.select("article, .post, .blog-post, .entry")

for post in blog_posts:
    try:
        # 提取标题
        title_element = post.select_one("h1, h2, .post-title, .entry-title, .title")
        title = title_element.get_text().strip() if title_element else ""
        
        # 提取链接
        link_element = title_element.select_one("a") if title_element else post.select_one("a")
        url = ""
        if link_element:
            url = link_element.get("href", "")
            if url and not url.startswith("http"):
                url = urljoin(response_data.get("url", ""), url)
        
        # 提取内容摘要
        content_element = post.select_one(".post-content, .entry-content, .content, .excerpt")
        content = ""
        if content_element:
            content = content_element.get_text().strip()[:300]
        
        # 提取发布时间
        time_element = post.select_one(".post-date, .entry-date, .date, time")
        publish_time = ""
        if time_element:
            publish_time = time_element.get_text().strip()
            if not publish_time:
                publish_time = time_element.get("datetime", "")
        
        # 提取作者
        author_element = post.select_one(".post-author, .entry-author, .author, .by")
        author = author_element.get_text().strip() if author_element else ""
        
        # 提取标签
        tag_elements = post.select(".tag, .tags a, .post-tags a")
        tags = [tag.get_text().strip() for tag in tag_elements]
        
        # 提取分类
        category_element = post.select_one(".category, .post-category")
        category = category_element.get_text().strip() if category_element else ""
        
        # 创建文章对象
        if title and url:
            article = ArticleData(
                title=title,
                url=url,
                content=content,
                author=author,
                publish_time=publish_time,
                category=category,
                tags=tags
            )
            articles.append(article)
            
    except Exception as e:
        print(f"提取博客文章时出错: {e}")
        continue
'''

# API接口模板
API_TEMPLATE = '''
"""
API接口数据提取脚本

适用于返回JSON格式数据的API接口
"""

try:
    # 解析JSON数据
    if isinstance(response_data.get("content"), str):
        data = json.loads(response_data["content"])
    else:
        data = response_data.get("content", {})
    
    # 根据API响应结构提取文章列表
    # 常见的数据结构路径
    articles_data = None
    possible_paths = [
        data.get("data", {}).get("list", []),
        data.get("data", []),
        data.get("list", []),
        data.get("items", []),
        data.get("articles", []),
        data.get("results", [])
    ]
    
    for path in possible_paths:
        if isinstance(path, list) and path:
            articles_data = path
            break
    
    if not articles_data:
        articles_data = data if isinstance(data, list) else []
    
    # 提取每篇文章的信息
    for item in articles_data:
        try:
            # 根据API字段映射提取信息
            title = item.get("title", "") or item.get("name", "") or item.get("subject", "")
            url = item.get("url", "") or item.get("link", "") or item.get("href", "")
            content = item.get("content", "") or item.get("summary", "") or item.get("description", "")
            author = item.get("author", "") or item.get("writer", "") or item.get("user", {}).get("name", "")
            publish_time = item.get("publish_time", "") or item.get("created_at", "") or item.get("time", "")
            category = item.get("category", "") or item.get("type", "") or item.get("section", "")
            
            # 处理嵌套结构
            if isinstance(author, dict):
                author = author.get("name", "") or author.get("username", "")
            
            # 提取数字统计
            read_count = item.get("read_count", 0) or item.get("views", 0) or item.get("pv", 0)
            like_count = item.get("like_count", 0) or item.get("likes", 0) or item.get("praise", 0)
            comment_count = item.get("comment_count", 0) or item.get("comments", 0) or item.get("reply", 0)
            
            # 创建文章对象
            if title:
                article = ArticleData(
                    title=title,
                    url=url,
                    content=content,
                    author=author,
                    publish_time=publish_time,
                    category=category,
                    read_count=read_count,
                    like_count=like_count,
                    comment_count=comment_count
                )
                articles.append(article)
                
        except Exception as e:
            print(f"提取API文章时出错: {e}")
            continue
    
    # 提取分页信息
    try:
        pagination_data = data.get("pagination", {}) or data.get("page", {})
        if pagination_data:
            current_page = pagination_data.get("current", 1) or pagination_data.get("page", 1)
            total_pages = pagination_data.get("total", 1) or pagination_data.get("pages", 1)
            has_next = pagination_data.get("has_next", False) or current_page < total_pages
            
            pagination = PaginationInfo(
                current_page=current_page,
                total_pages=total_pages,
                has_next=has_next
            )
    except:
        pass
        
except Exception as e:
    print(f"解析API数据时出错: {e}")
'''

# 脚本模板字典
SCRIPT_TEMPLATES = {
    "basic": {
        "name": "基础模板",
        "description": "适用于大多数网站的基础文章提取模板",
        "script": BASIC_TEMPLATE
    },
    "news": {
        "name": "新闻网站模板", 
        "description": "适用于新闻网站的文章列表页面",
        "script": NEWS_WEBSITE_TEMPLATE
    },
    "blog": {
        "name": "博客模板",
        "description": "适用于个人博客或博客平台",
        "script": BLOG_TEMPLATE
    },
    "api": {
        "name": "API接口模板",
        "description": "适用于返回JSON格式数据的API接口",
        "script": API_TEMPLATE
    }
}


def get_template(template_name: str) -> str:
    """获取指定的脚本模板"""
    template = SCRIPT_TEMPLATES.get(template_name)
    if template:
        return template["script"]
    return BASIC_TEMPLATE


def list_templates() -> dict:
    """列出所有可用的脚本模板"""
    return {
        name: {
            "name": template["name"],
            "description": template["description"]
        }
        for name, template in SCRIPT_TEMPLATES.items()
    }
