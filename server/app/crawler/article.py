"""
通用网页文章爬取模块

本模块提供通用的网页文章爬取功能，支持：
1. 自定义Python脚本适配不同网站结构
2. 多种数据提取策略（CSS选择器、正则表达式、LLM）
3. 分页处理和批量爬取
4. 反爬虫机制和安全控制
5. 完整的错误处理和日志记录

主要函数：
- crawl_articles: 通用文章爬取函数
- crawl_articles_with_pagination: 支持分页的文章爬取
- batch_crawl_articles: 批量爬取多个URL
"""

import asyncio
import time
import logging
from typing import List, Dict, Any, Optional, Union
from urllib.parse import urljoin, urlparse
import json

import requests
from bs4 import BeautifulSoup
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, BrowserConfig
from crawl4ai import Crawl4aiDockerClient, LLMExtractionStrategy, LLMConfig
from loguru import logger

from .models import CrawlConfig, CrawlResult, ArticleData, PaginationInfo, ScriptExecutionResult, ExtractionStrategy
from .utils import (
    SafeScriptExecutor,
    AntiCrawlerHandler,
    DataCleaner,
    URLBuilder,
    generate_request_id,
    format_execution_time,
)
from .templates import get_template


class ArticleCrawler:
    """文章爬取器主类"""

    def __init__(self):
        self.script_executor = SafeScriptExecutor()
        self.anti_crawler = AntiCrawlerHandler()
        self.data_cleaner = DataCleaner()
        self.url_builder = URLBuilder()

    async def crawl_articles(self, config: CrawlConfig) -> CrawlResult:
        """
        通用文章爬取函数

        Args:
            config: 爬取配置对象

        Returns:
            CrawlResult: 爬取结果对象
        """
        request_id = generate_request_id()
        start_time = time.time()

        logger.info(f"[{request_id}] 开始爬取文章: {config.url}")

        try:
            # 应用反爬虫延迟
            await self.anti_crawler.apply_delay(config)

            # 根据配置选择爬取方式
            if config.use_browser:
                response_data = await self._crawl_with_browser(config)
            else:
                response_data = await self._crawl_with_requests(config)

            if not response_data["success"]:
                return CrawlResult(
                    success=False, error_message=response_data["error"], execution_time=time.time() - start_time
                )

            # 解析HTML内容
            soup = BeautifulSoup(response_data["content"], "html.parser")

            # 根据提取策略处理数据
            extraction_result = await self._extract_articles(config, soup, response_data)

            if not extraction_result.success:
                return CrawlResult(
                    success=False,
                    error_message=extraction_result.error_message,
                    execution_time=time.time() - start_time,
                )

            # 数据清洗和验证
            cleaned_articles = self._clean_articles(extraction_result.data, config.url)

            execution_time = time.time() - start_time

            logger.info(
                f"[{request_id}] 爬取完成，提取到 {len(cleaned_articles)} 篇文章，耗时 {format_execution_time(execution_time)}"
            )

            return CrawlResult(
                success=True,
                articles=cleaned_articles,
                pagination=extraction_result.pagination,
                total_pages_crawled=1,
                total_articles=len(cleaned_articles),
                execution_time=execution_time,
                metadata={
                    "request_id": request_id,
                    "url": config.url,
                    "extraction_strategy": config.extraction_strategy.value,
                    "use_browser": config.use_browser,
                },
            )

        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"爬取过程中发生错误: {str(e)}"
            logger.error(f"[{request_id}] {error_msg}", exc_info=True)

            return CrawlResult(
                success=False,
                error_message=error_msg,
                execution_time=execution_time,
                metadata={"request_id": request_id},
            )

    async def _crawl_with_browser(self, config: CrawlConfig) -> Dict[str, Any]:
        """使用浏览器爬取"""
        try:
            # 准备浏览器配置
            browser_config = BrowserConfig(headless=config.headless, verbose=False)

            # 准备爬取配置
            crawler_config = CrawlerRunConfig(
                excluded_tags=["script", "style", "noscript"],
                wait_for=config.wait_for_selector,
                delay_before_return_html=config.wait_time,
            )

            # 使用crawl4ai进行爬取
            async with Crawl4aiDockerClient(
                base_url="http://crawl:11235", timeout=config.timeout, verbose=False
            ) as client:
                result = await client.crawl(
                    urls=[config.url], browser_config=browser_config, crawler_config=crawler_config
                )

                if result.success:
                    return {"success": True, "content": result.html, "url": config.url, "status_code": 200}
                else:
                    return {"success": False, "error": result.error_message or "浏览器爬取失败"}

        except Exception as e:
            return {"success": False, "error": f"浏览器爬取异常: {str(e)}"}

    async def _crawl_with_requests(self, config: CrawlConfig) -> Dict[str, Any]:
        """使用requests库爬取"""
        try:
            # 准备请求参数
            headers = self.anti_crawler.prepare_headers(config)

            # 发送请求
            response = requests.request(
                method=config.method.value,
                url=config.url,
                headers=headers,
                params=config.params,
                data=config.data,
                cookies=config.cookies,
                timeout=config.timeout,
                allow_redirects=True,
            )

            response.raise_for_status()

            return {
                "success": True,
                "content": response.text,
                "url": response.url,
                "status_code": response.status_code,
                "headers": dict(response.headers),
            }

        except Exception as e:
            return {"success": False, "error": f"HTTP请求失败: {str(e)}"}

    async def _extract_articles(
        self, config: CrawlConfig, soup: BeautifulSoup, response_data: Dict[str, Any]
    ) -> ScriptExecutionResult:
        """根据配置的策略提取文章"""

        if config.extraction_strategy == ExtractionStrategy.CUSTOM_SCRIPT:
            return await self._extract_with_custom_script(config, soup, response_data)
        elif config.extraction_strategy == ExtractionStrategy.CSS_SELECTOR:
            return await self._extract_with_css_selector(config, soup, response_data)
        elif config.extraction_strategy == ExtractionStrategy.REGEX:
            return await self._extract_with_regex(config, soup, response_data)
        elif config.extraction_strategy == ExtractionStrategy.LLM:
            return await self._extract_with_llm(config, soup, response_data)
        else:
            return ScriptExecutionResult(success=False, error_message=f"不支持的提取策略: {config.extraction_strategy}")

    async def _extract_with_custom_script(
        self, config: CrawlConfig, soup: BeautifulSoup, response_data: Dict[str, Any]
    ) -> ScriptExecutionResult:
        """使用自定义脚本提取"""
        if not config.custom_script:
            return ScriptExecutionResult(success=False, error_message="未提供自定义脚本")

        return self.script_executor.execute_script(config.custom_script, soup, response_data)

    async def _extract_with_css_selector(
        self, config: CrawlConfig, soup: BeautifulSoup, response_data: Dict[str, Any]
    ) -> ScriptExecutionResult:
        """使用CSS选择器提取"""
        start_time = time.time()

        try:
            articles = []
            selectors = config.css_selectors or {}

            # 获取文章容器
            container_selector = selectors.get("container", "article, .article, .post, .news-item")
            article_containers = soup.select(container_selector)

            for container in article_containers:
                try:
                    # 提取各字段
                    title = self._extract_text_by_selector(container, selectors.get("title", "h1, h2, h3, .title"))
                    url = self._extract_attr_by_selector(container, selectors.get("link", "a"), "href")
                    content = self._extract_text_by_selector(
                        container, selectors.get("content", ".content, .summary, p")
                    )
                    author = self._extract_text_by_selector(container, selectors.get("author", ".author, .writer"))
                    publish_time = self._extract_text_by_selector(
                        container, selectors.get("time", ".time, .date, time")
                    )
                    category = self._extract_text_by_selector(container, selectors.get("category", ".category, .tag"))

                    # 处理相对URL
                    if url and not url.startswith("http"):
                        url = urljoin(response_data.get("url", ""), url)

                    if title and url:
                        article = ArticleData(
                            title=title,
                            url=url,
                            content=content,
                            author=author,
                            publish_time=publish_time,
                            category=category,
                        )
                        articles.append(article)

                except Exception as e:
                    logger.warning(f"CSS选择器提取单篇文章失败: {e}")
                    continue

            execution_time = time.time() - start_time

            return ScriptExecutionResult(
                success=True, data=articles, execution_time=execution_time, extracted_count=len(articles)
            )

        except Exception as e:
            execution_time = time.time() - start_time
            return ScriptExecutionResult(
                success=False, error_message=f"CSS选择器提取失败: {str(e)}", execution_time=execution_time
            )

    async def _extract_with_regex(
        self, config: CrawlConfig, soup: BeautifulSoup, response_data: Dict[str, Any]
    ) -> ScriptExecutionResult:
        """使用正则表达式提取"""
        start_time = time.time()

        try:
            import re

            articles = []
            patterns = config.regex_patterns or {}
            html_content = str(soup)

            # 获取文章匹配模式
            article_pattern = patterns.get("article", r"<article[^>]*>(.*?)</article>")
            article_matches = re.findall(article_pattern, html_content, re.DOTALL | re.IGNORECASE)

            for match in article_matches:
                try:
                    # 提取标题
                    title_pattern = patterns.get("title", r"<h[1-6][^>]*>(.*?)</h[1-6]>")
                    title_match = re.search(title_pattern, match, re.IGNORECASE)
                    title = self.data_cleaner.clean_text(title_match.group(1)) if title_match else ""

                    # 提取链接
                    link_pattern = patterns.get("link", r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>')
                    link_match = re.search(link_pattern, match, re.IGNORECASE)
                    url = link_match.group(1) if link_match else ""

                    if url and not url.startswith("http"):
                        url = urljoin(response_data.get("url", ""), url)

                    # 提取内容
                    content_pattern = patterns.get("content", r"<p[^>]*>(.*?)</p>")
                    content_matches = re.findall(content_pattern, match, re.IGNORECASE)
                    content = " ".join([self.data_cleaner.clean_text(c) for c in content_matches[:3]])

                    if title and url:
                        article = ArticleData(title=title, url=url, content=content)
                        articles.append(article)

                except Exception as e:
                    logger.warning(f"正则表达式提取单篇文章失败: {e}")
                    continue

            execution_time = time.time() - start_time

            return ScriptExecutionResult(
                success=True, data=articles, execution_time=execution_time, extracted_count=len(articles)
            )

        except Exception as e:
            execution_time = time.time() - start_time
            return ScriptExecutionResult(
                success=False, error_message=f"正则表达式提取失败: {str(e)}", execution_time=execution_time
            )

    async def _extract_with_llm(
        self, config: CrawlConfig, soup: BeautifulSoup, response_data: Dict[str, Any]
    ) -> ScriptExecutionResult:
        """使用LLM提取"""
        start_time = time.time()

        try:
            # 使用crawl4ai的LLM提取功能
            llm_config = LLMConfig(
                provider="deepseek/deepseek-chat",
                api_token="***********************************",
            )

            extraction_strategy = LLMExtractionStrategy(
                llm_config=llm_config,
                schema=ArticleData.model_json_schema(),
                extraction_type="schema",
                instruction="""
                请从网页内容中提取文章信息，包括：
                - title: 文章标题
                - url: 文章链接
                - content: 文章内容摘要（200字以内）
                - author: 作者
                - publish_time: 发布时间
                - category: 分类

                请返回JSON格式的文章列表。
                """,
            )

            # 这里需要重新调用crawl4ai进行LLM提取
            # 由于架构限制，暂时返回未实现状态
            return ScriptExecutionResult(
                success=False, error_message="LLM提取策略需要重新设计架构，建议使用自定义脚本实现"
            )

        except Exception as e:
            execution_time = time.time() - start_time
            return ScriptExecutionResult(
                success=False, error_message=f"LLM提取失败: {str(e)}", execution_time=execution_time
            )

    def _extract_text_by_selector(self, container, selector: str) -> str:
        """通过选择器提取文本"""
        try:
            element = container.select_one(selector)
            return self.data_cleaner.clean_text(element.get_text()) if element else ""
        except:
            return ""

    def _extract_attr_by_selector(self, container, selector: str, attr: str) -> str:
        """通过选择器提取属性"""
        try:
            element = container.select_one(selector)
            return element.get(attr, "") if element else ""
        except:
            return ""

    def _clean_articles(self, articles: List[ArticleData], base_url: str) -> List[ArticleData]:
        """清洗文章数据"""
        cleaned_articles = []

        for article in articles:
            try:
                # 清洗文本字段
                article.title = self.data_cleaner.clean_text(article.title)
                article.content = self.data_cleaner.clean_text(article.content) if article.content else None
                article.summary = self.data_cleaner.clean_text(article.summary) if article.summary else None
                article.author = self.data_cleaner.clean_text(article.author) if article.author else None
                article.category = self.data_cleaner.clean_text(article.category) if article.category else None

                # 清洗URL
                article.url = self.data_cleaner.clean_url(article.url, base_url)
                article.image_url = (
                    self.data_cleaner.clean_url(article.image_url, base_url) if article.image_url else None
                )

                # 验证数据完整性
                if article.title:
                    cleaned_articles.append(article)

            except Exception as e:
                logger.warning(f"清洗文章数据失败: {e}")
                continue
        logger.info(f"清洗完成，提取到 {len(cleaned_articles)} 篇有效文章")
        return cleaned_articles

    async def crawl_articles_with_pagination(self, config: CrawlConfig) -> CrawlResult:
        """
        支持分页的文章爬取函数

        Args:
            config: 爬取配置对象，需要启用分页功能

        Returns:
            CrawlResult: 包含所有页面文章的爬取结果
        """
        if not config.enable_pagination:
            return await self.crawl_articles(config)

        request_id = generate_request_id()
        start_time = time.time()
        all_articles = []
        current_page = 1
        total_pages_crawled = 0

        logger.info(f"[{request_id}] 开始分页爬取: {config.url}, 最大页数: {config.max_pages}")

        try:
            current_url = config.url

            while current_page <= config.max_pages:
                logger.info(f"[{request_id}] 爬取第 {current_page} 页: {current_url}")

                # 更新配置中的URL
                page_config = config.model_copy()
                page_config.url = current_url

                # 爬取当前页
                page_result = await self.crawl_articles(page_config)
                total_pages_crawled += 1

                if not page_result.success:
                    logger.warning(f"[{request_id}] 第 {current_page} 页爬取失败: {page_result.error_message}")
                    break

                # 添加文章到总列表
                all_articles.extend(page_result.articles)
                logger.info(f"[{request_id}] 第 {current_page} 页提取到 {len(page_result.articles)} 篇文章")

                # 检查是否有下一页
                if not page_result.pagination or not page_result.pagination.has_next:
                    logger.info(f"[{request_id}] 没有更多页面，停止爬取")
                    break

                # 获取下一页URL
                if page_result.pagination.next_page_url:
                    current_url = page_result.pagination.next_page_url
                else:
                    # 使用URL构建器生成下一页URL
                    current_url = self.url_builder.build_paginated_url(
                        config.url, current_page + 1, page_result.pagination.next_page_params
                    )

                current_page += 1

                # 应用分页间隔
                await self.anti_crawler.apply_delay(config)

            execution_time = time.time() - start_time

            logger.info(
                f"[{request_id}] 分页爬取完成，共爬取 {total_pages_crawled} 页，"
                f"提取到 {len(all_articles)} 篇文章，耗时 {format_execution_time(execution_time)}"
            )

            return CrawlResult(
                success=True,
                articles=all_articles,
                total_pages_crawled=total_pages_crawled,
                total_articles=len(all_articles),
                execution_time=execution_time,
                metadata={
                    "request_id": request_id,
                    "base_url": config.url,
                    "pages_crawled": total_pages_crawled,
                    "extraction_strategy": config.extraction_strategy.value,
                },
            )

        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"分页爬取过程中发生错误: {str(e)}"
            logger.error(f"[{request_id}] {error_msg}", exc_info=True)

            return CrawlResult(
                success=False,
                articles=all_articles,  # 返回已爬取的文章
                total_pages_crawled=total_pages_crawled,
                total_articles=len(all_articles),
                error_message=error_msg,
                execution_time=execution_time,
                metadata={"request_id": request_id},
            )

    async def batch_crawl_articles(self, configs: List[CrawlConfig]) -> List[CrawlResult]:
        """
        批量爬取多个URL的文章

        Args:
            configs: 爬取配置列表

        Returns:
            List[CrawlResult]: 爬取结果列表
        """
        request_id = generate_request_id()
        start_time = time.time()
        results = []

        logger.info(f"[{request_id}] 开始批量爬取 {len(configs)} 个URL")

        try:
            for i, config in enumerate(configs, 1):
                logger.info(f"[{request_id}] 处理第 {i}/{len(configs)} 个URL: {config.url}")

                try:
                    if config.enable_pagination:
                        result = await self.crawl_articles_with_pagination(config)
                    else:
                        result = await self.crawl_articles(config)

                    results.append(result)

                    if result.success:
                        logger.info(f"[{request_id}] URL {i} 爬取成功，提取到 {result.total_articles} 篇文章")
                    else:
                        logger.warning(f"[{request_id}] URL {i} 爬取失败: {result.error_message}")

                except Exception as e:
                    error_msg = f"处理URL {i} 时发生异常: {str(e)}"
                    logger.error(f"[{request_id}] {error_msg}")

                    results.append(
                        CrawlResult(success=False, error_message=error_msg, metadata={"url": config.url, "index": i})
                    )

                # 批量处理间隔
                if i < len(configs):
                    await self.anti_crawler.apply_delay(config)

            execution_time = time.time() - start_time
            successful_count = sum(1 for r in results if r.success)
            total_articles = sum(r.total_articles for r in results)

            logger.info(
                f"[{request_id}] 批量爬取完成，成功 {successful_count}/{len(configs)} 个URL，总计提取 {total_articles} 篇文章，耗时 {format_execution_time(execution_time)}"
            )

            return results

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"[{request_id}] 批量爬取过程中发生错误: {str(e)}", exc_info=True)
            return results


# 全局爬取器实例
_crawler_instance = None


def get_crawler() -> ArticleCrawler:
    """获取爬取器实例（单例模式）"""
    global _crawler_instance
    if _crawler_instance is None:
        _crawler_instance = ArticleCrawler()
    return _crawler_instance


# 便捷函数
async def crawl_articles(config: CrawlConfig) -> CrawlResult:
    """
    通用文章爬取函数（便捷接口）

    Args:
        config: 爬取配置对象

    Returns:
        CrawlResult: 爬取结果对象

    Example:
        ```python
        from app.crawler.article import crawl_articles
        from app.crawler.models import CrawlConfig, ExtractionStrategy

        config = CrawlConfig(
            url="https://example.com/news",
            extraction_strategy=ExtractionStrategy.CUSTOM_SCRIPT,
            custom_script='''
            # 提取文章的自定义脚本
            news_items = soup.select(".news-item")
            for item in news_items:
                title = item.select_one("h2").get_text().strip()
                url = item.select_one("a").get("href")
                articles.append(ArticleData(title=title, url=url))
            '''
        )

        result = await crawl_articles(config)
        if result.success:
            print(f"提取到 {len(result.articles)} 篇文章")
        ```
    """
    crawler = get_crawler()
    return await crawler.crawl_articles(config)


async def crawl_articles_with_pagination(config: CrawlConfig) -> CrawlResult:
    """
    支持分页的文章爬取函数（便捷接口）

    Args:
        config: 爬取配置对象，需要启用分页功能

    Returns:
        CrawlResult: 包含所有页面文章的爬取结果
    """
    crawler = get_crawler()
    return await crawler.crawl_articles_with_pagination(config)


async def batch_crawl_articles(configs: List[CrawlConfig]) -> List[CrawlResult]:
    """
    批量爬取多个URL的文章（便捷接口）

    Args:
        configs: 爬取配置列表

    Returns:
        List[CrawlResult]: 爬取结果列表
    """
    crawler = get_crawler()
    return await crawler.batch_crawl_articles(configs)
