"""
通用网页文章爬取模块

本模块提供强大而灵活的网页文章爬取功能，支持：
- 自定义Python脚本适配不同网站结构
- 多种数据提取策略
- 分页处理和批量爬取
- 反爬虫机制和安全控制

主要导出：
- crawl_articles: 通用文章爬取函数
- crawl_articles_with_pagination: 支持分页的文章爬取
- batch_crawl_articles: 批量爬取多个URL
- CrawlConfig: 爬取配置模型
- CrawlResult: 爬取结果模型
- ArticleData: 文章数据模型
- ExtractionStrategy: 提取策略枚举
"""

from .article import crawl_articles, crawl_articles_with_pagination, batch_crawl_articles, ArticleCrawler, get_crawler

from .models import (
    CrawlConfig,
    CrawlResult,
    ArticleData,
    PaginationInfo,
    ScriptExecutionResult,
    ExtractionStrategy,
    CrawlMethod,
    ResponseFormat,
)

from .templates import get_template, list_templates, SCRIPT_TEMPLATES

from .utils import (
    SafeScriptExecutor,
    AntiCrawlerHandler,
    DataCleaner,
    URLBuilder,
    generate_request_id,
    format_execution_time,
)

__all__ = [
    # 主要函数
    "crawl_articles",
    "crawl_articles_with_pagination",
    "batch_crawl_articles",
    "ArticleCrawler",
    "get_crawler",
    # 数据模型
    "CrawlConfig",
    "CrawlResult",
    "ArticleData",
    "PaginationInfo",
    "ScriptExecutionResult",
    # 枚举类型
    "ExtractionStrategy",
    "CrawlMethod",
    "ResponseFormat",
    # 模板相关
    "get_template",
    "list_templates",
    "SCRIPT_TEMPLATES",
    # 工具类
    "SafeScriptExecutor",
    "AntiCrawlerHandler",
    "DataCleaner",
    "URLBuilder",
    "generate_request_id",
    "format_execution_time",
]

# 版本信息
__version__ = "1.0.0"
__author__ = "Crawler Team"
__description__ = "通用网页文章爬取模块"
