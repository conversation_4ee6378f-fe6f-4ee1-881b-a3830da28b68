# 通用网页文章爬取模块

本模块提供了一个强大而灵活的网页文章爬取解决方案，支持多种提取策略和自定义脚本，能够适配各种不同结构的网站。

## 🚀 核心特性

- **多种提取策略**: 支持自定义脚本、CSS选择器、XPath、正则表达式、LLM等多种数据提取方式
- **自定义脚本执行**: 安全的Python脚本执行环境，用户可编写少量代码适配不同网站结构
- **分页处理**: 自动识别和处理分页，支持批量爬取多页内容
- **反爬虫机制**: 内置User-Agent轮换、请求延迟、代理支持等反爬虫功能
- **数据清洗**: 自动清洗和验证提取的数据，确保数据质量
- **错误处理**: 完善的异常处理和日志记录机制
- **批量处理**: 支持同时爬取多个网站

## 📦 模块结构

```
crawler/
├── __init__.py          # 模块初始化
├── article.py           # 核心爬取功能
├── models.py            # 数据模型定义
├── utils.py             # 工具函数
├── templates.py         # 脚本模板和示例
├── examples.py          # 使用示例
└── README.md           # 使用说明
```

## 🛠️ 快速开始

### 1. 基础使用

```python
from app.crawler.article import crawl_articles
from app.crawler.models import CrawlConfig, ExtractionStrategy

# 创建爬取配置
config = CrawlConfig(
    url="https://example.com/news",
    extraction_strategy=ExtractionStrategy.CUSTOM_SCRIPT,
    custom_script='''
    # 提取新闻文章
    news_items = soup.select(".news-item")
    for item in news_items:
        title = item.select_one("h2").get_text().strip()
        url = item.select_one("a").get("href")
        articles.append(ArticleData(title=title, url=url))
    '''
)

# 执行爬取
result = await crawl_articles(config)

if result.success:
    print(f"提取到 {len(result.articles)} 篇文章")
    for article in result.articles:
        print(f"- {article.title}: {article.url}")
else:
    print(f"爬取失败: {result.error_message}")
```

### 2. 使用CSS选择器

```python
config = CrawlConfig(
    url="https://example.com/articles",
    extraction_strategy=ExtractionStrategy.CSS_SELECTOR,
    css_selectors={
        "container": ".article-list .item",
        "title": "h2.title",
        "link": "a",
        "content": ".summary",
        "author": ".author",
        "time": ".date"
    }
)

result = await crawl_articles(config)
```

### 3. 分页爬取

```python
config = CrawlConfig(
    url="https://example.com/news/page/1",
    extraction_strategy=ExtractionStrategy.CUSTOM_SCRIPT,
    custom_script=your_script,
    enable_pagination=True,
    max_pages=5,  # 最多爬取5页
    request_delay=2.0  # 每页间隔2秒
)

result = await crawl_articles_with_pagination(config)
print(f"共爬取 {result.total_pages_crawled} 页，{result.total_articles} 篇文章")
```

## 📝 自定义脚本编写指南

### 脚本执行环境

在自定义脚本中，您可以使用以下预定义变量和函数：

**可用变量:**
- `soup`: BeautifulSoup对象，用于HTML解析
- `response_data`: 响应数据字典，包含URL、状态码等信息
- `articles`: 文章列表，脚本需要将提取的文章添加到此列表
- `pagination`: 分页信息，可选设置

**可用类:**
- `ArticleData`: 文章数据模型
- `PaginationInfo`: 分页信息模型

**可用工具函数:**
- `urljoin`: URL拼接
- `urlparse`: URL解析
- `re`: 正则表达式模块
- `json`: JSON处理模块
- `datetime`: 日期时间模块

### 脚本模板

#### 1. 基础模板

```python
# 查找文章容器
article_containers = soup.select("文章容器选择器")

for container in article_containers:
    try:
        # 提取标题
        title_element = container.select_one("标题选择器")
        title = title_element.get_text().strip() if title_element else ""
        
        # 提取链接
        link_element = container.select_one("链接选择器")
        url = link_element.get("href", "") if link_element else ""
        if url and not url.startswith("http"):
            url = urljoin(response_data.get("url", ""), url)
        
        # 提取内容
        content_element = container.select_one("内容选择器")
        content = content_element.get_text().strip() if content_element else ""
        
        # 创建文章对象
        if title and url:
            article = ArticleData(
                title=title,
                url=url,
                content=content
            )
            articles.append(article)
            
    except Exception as e:
        print(f"提取文章时出错: {e}")
        continue
```

#### 2. 新闻网站模板

```python
# 新闻网站通用提取脚本
news_items = soup.select("div.news-item, .article-item, .news-list li")

for item in news_items:
    try:
        # 提取标题 - 尝试多个选择器
        title_selectors = ["h1", "h2", "h3", ".title", ".headline"]
        title = ""
        for selector in title_selectors:
            title_element = item.select_one(selector)
            if title_element:
                title = title_element.get_text().strip()
                break
        
        # 提取链接
        link_element = item.select_one("a")
        url = link_element.get("href", "") if link_element else ""
        if url and not url.startswith("http"):
            url = urljoin(response_data.get("url", ""), url)
        
        # 提取摘要
        summary_element = item.select_one(".summary, .excerpt, .description")
        summary = summary_element.get_text().strip()[:200] if summary_element else ""
        
        # 提取时间
        time_element = item.select_one(".time, .date, .publish-time")
        publish_time = time_element.get_text().strip() if time_element else ""
        
        # 提取作者
        author_element = item.select_one(".author, .writer")
        author = author_element.get_text().strip() if author_element else ""
        
        if title and url:
            article = ArticleData(
                title=title,
                url=url,
                summary=summary,
                author=author,
                publish_time=publish_time
            )
            articles.append(article)
            
    except Exception as e:
        continue
```

#### 3. API接口模板

```python
# API接口数据提取脚本
try:
    # 解析JSON数据
    if isinstance(response_data.get("content"), str):
        data = json.loads(response_data["content"])
    else:
        data = response_data.get("content", {})
    
    # 提取文章列表
    articles_data = data.get("data", {}).get("list", [])
    
    for item in articles_data:
        try:
            title = item.get("title", "")
            url = item.get("url", "")
            content = item.get("content", "")
            author = item.get("author", "")
            publish_time = item.get("publish_time", "")
            
            if title:
                article = ArticleData(
                    title=title,
                    url=url,
                    content=content,
                    author=author,
                    publish_time=publish_time
                )
                articles.append(article)
                
        except Exception as e:
            continue
            
except Exception as e:
    print(f"解析API数据时出错: {e}")
```

### 分页处理

如果需要处理分页，在脚本中设置`pagination`变量：

```python
# 提取分页信息
try:
    next_link = soup.select_one(".pagination .next, a:contains('下一页')")
    if next_link:
        next_url = next_link.get("href", "")
        if next_url and not next_url.startswith("http"):
            next_url = urljoin(response_data.get("url", ""), next_url)
        
        pagination = PaginationInfo(
            has_next=True,
            next_page_url=next_url
        )
except:
    pass
```

## 🔧 配置参数说明

### CrawlConfig 主要参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `url` | str | 必填 | 目标URL |
| `method` | CrawlMethod | GET | HTTP方法 |
| `extraction_strategy` | ExtractionStrategy | CUSTOM_SCRIPT | 提取策略 |
| `custom_script` | str | None | 自定义Python脚本 |
| `enable_pagination` | bool | False | 是否启用分页 |
| `max_pages` | int | 10 | 最大页数 |
| `request_delay` | float | 1.0 | 请求间隔(秒) |
| `timeout` | int | 30 | 请求超时时间 |
| `use_browser` | bool | False | 是否使用浏览器渲染 |
| `user_agent` | str | None | 自定义User-Agent |

## 🛡️ 安全机制

### 脚本安全

- 禁用危险函数：`exec`, `eval`, `open`, `import`等
- 限制可导入模块：只允许安全的内置模块
- 沙箱执行环境：隔离脚本执行上下文

### 反爬虫机制

- User-Agent轮换
- 请求间隔控制
- 随机延迟
- 代理支持（可选）

## 📊 数据模型

### ArticleData

```python
class ArticleData(BaseModel):
    title: str                    # 文章标题
    url: str                      # 文章链接
    content: Optional[str]        # 文章内容
    summary: Optional[str]        # 文章摘要
    author: Optional[str]         # 作者
    publish_time: Optional[str]   # 发布时间
    category: Optional[str]       # 分类
    tags: Optional[List[str]]     # 标签列表
    image_url: Optional[str]      # 封面图片
    read_count: Optional[int]     # 阅读数
    like_count: Optional[int]     # 点赞数
    comment_count: Optional[int]  # 评论数
    extra_data: Optional[Dict]    # 额外数据
```

### CrawlResult

```python
class CrawlResult(BaseModel):
    success: bool                 # 爬取是否成功
    articles: List[ArticleData]   # 文章列表
    pagination: Optional[PaginationInfo]  # 分页信息
    total_pages_crawled: int      # 已爬取页数
    total_articles: int           # 总文章数
    error_message: Optional[str]  # 错误信息
    execution_time: float         # 执行时间
    metadata: Optional[Dict]      # 元数据
```

## 🔍 调试和日志

模块内置了完善的日志记录功能，可以通过以下方式查看详细信息：

```python
import logging
logging.basicConfig(level=logging.INFO)

# 执行爬取时会输出详细日志
result = await crawl_articles(config)
```

## 🚨 注意事项

1. **遵守robots.txt**: 请确保遵守目标网站的robots.txt规则
2. **合理设置延迟**: 避免对目标服务器造成过大压力
3. **脚本安全**: 自定义脚本会在受限环境中执行，但仍需谨慎编写
4. **数据使用**: 请合法合规使用爬取的数据
5. **错误处理**: 建议在生产环境中添加适当的错误处理和重试机制

## 📚 更多示例

查看 `examples.py` 文件获取更多详细的使用示例，包括：
- 新闻网站爬取
- 博客网站爬取
- API接口爬取
- 分页爬取
- 批量爬取

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个模块！
