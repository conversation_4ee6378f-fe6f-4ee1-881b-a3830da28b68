"""
爬取状态管理模块

本模块提供爬取状态的记录、存储和管理功能，支持定时任务中的增量爬取。
主要功能：
1. 爬取状态的持久化存储
2. 多种增量判断策略
3. 状态的自动更新和清理
4. 灵活的状态查询接口
"""

import json
import time
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Union
from pathlib import Path

from loguru import logger
from pydantic import BaseModel, Field
import asyncio
import aiofiles
import logging


class CrawlState(BaseModel):
    """爬取状态模型"""

    # 基础信息
    task_id: str = Field(..., description="任务ID")
    url: str = Field(..., description="爬取URL")
    url_hash: str = Field(..., description="URL哈希值")

    # 时间信息
    last_crawl_time: datetime = Field(..., description="最后爬取时间")
    created_time: datetime = Field(..., description="创建时间")
    updated_time: datetime = Field(..., description="更新时间")

    # 爬取状态
    last_page: int = Field(1, description="最后爬取的页码")
    total_articles: int = Field(0, description="累计爬取文章数")
    last_article_id: Optional[str] = Field(None, description="最后一篇文章的ID")
    last_article_time: Optional[str] = Field(None, description="最后一篇文章的时间")
    last_article_title: Optional[str] = Field(None, description="最后一篇文章的标题")

    # 自定义状态数据
    custom_state: Dict[str, Any] = Field(default_factory=dict, description="自定义状态数据")

    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")

    @classmethod
    def create_new(cls, task_id: str, url: str, **kwargs) -> "CrawlState":
        """创建新的爬取状态"""
        url_hash = hashlib.md5(url.encode()).hexdigest()
        now = datetime.now()

        return cls(
            task_id=task_id,
            url=url,
            url_hash=url_hash,
            last_crawl_time=now,
            created_time=now,
            updated_time=now,
            **kwargs,
        )

    def update_state(self, **kwargs):
        """更新状态"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_time = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.model_dump()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "CrawlState":
        """从字典创建"""
        return cls(**data)


class IncrementalStrategy:
    """增量爬取策略"""

    @staticmethod
    def by_time(state: CrawlState, article_time: str, time_format: str = "%Y-%m-%d %H:%M:%S") -> bool:
        """基于时间的增量判断"""
        try:
            if not state.last_article_time:
                return True

            article_dt = datetime.strptime(article_time, time_format)
            last_dt = datetime.strptime(state.last_article_time, time_format)

            return article_dt > last_dt
        except:
            return True

    @staticmethod
    def by_id(state: CrawlState, article_id: str) -> bool:
        """基于ID的增量判断"""
        return state.last_article_id != article_id

    @staticmethod
    def by_title(state: CrawlState, article_title: str) -> bool:
        """基于标题的增量判断"""
        return state.last_article_title != article_title

    @staticmethod
    def by_custom(state: CrawlState, custom_key: str, custom_value: Any) -> bool:
        """基于自定义字段的增量判断"""
        return state.custom_state.get(custom_key) != custom_value

    @staticmethod
    def by_content_hash(state: CrawlState, content: str) -> bool:
        """基于内容哈希的增量判断"""
        content_hash = hashlib.md5(content.encode()).hexdigest()
        return state.custom_state.get("last_content_hash") != content_hash


class StateManager:
    """状态管理器"""

    def __init__(self, storage_path: str = "crawler_states"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True)
        self._states_cache: Dict[str, CrawlState] = {}

    def _get_state_file_path(self, task_id: str) -> Path:
        """获取状态文件路径"""
        return self.storage_path / f"{task_id}.json"

    async def save_state(self, state: CrawlState) -> bool:
        """保存状态到文件"""
        try:
            file_path = self._get_state_file_path(state.task_id)

            # 更新缓存
            self._states_cache[state.task_id] = state

            # 保存到文件
            async with aiofiles.open(file_path, "w", encoding="utf-8") as f:
                await f.write(json.dumps(state.to_dict(), ensure_ascii=False, indent=2, default=str))

            logger.info(f"状态已保存: {state.task_id}")
            return True

        except Exception as e:
            logger.error(f"保存状态失败: {e}")
            return False

    async def load_state(self, task_id: str) -> Optional[CrawlState]:
        """从文件加载状态"""
        try:
            # 先检查缓存
            if task_id in self._states_cache:
                return self._states_cache[task_id]

            file_path = self._get_state_file_path(task_id)

            if not file_path.exists():
                return None

            async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
                content = await f.read()
                data = json.loads(content)

                # 处理datetime字段
                for field in ["last_crawl_time", "created_time", "updated_time"]:
                    if field in data and isinstance(data[field], str):
                        data[field] = datetime.fromisoformat(data[field].replace("Z", "+00:00"))

                state = CrawlState.from_dict(data)

                # 更新缓存
                self._states_cache[task_id] = state

                return state

        except Exception as e:
            logger.error(f"加载状态失败: {e}")
            return None

    async def get_or_create_state(self, task_id: str, url: str, **kwargs) -> CrawlState:
        """获取或创建状态"""
        state = await self.load_state(task_id)

        if state is None:
            state = CrawlState.create_new(task_id, url, **kwargs)
            await self.save_state(state)

        return state

    async def update_state(self, task_id: str, **kwargs) -> bool:
        """更新状态"""
        state = await self.load_state(task_id)

        if state is None:
            logger.warning(f"状态不存在: {task_id}")
            return False

        state.update_state(**kwargs)
        return await self.save_state(state)

    async def delete_state(self, task_id: str) -> bool:
        """删除状态"""
        try:
            file_path = self._get_state_file_path(task_id)

            if file_path.exists():
                file_path.unlink()

            # 从缓存中删除
            if task_id in self._states_cache:
                del self._states_cache[task_id]

            logger.info(f"状态已删除: {task_id}")
            return True

        except Exception as e:
            logger.error(f"删除状态失败: {e}")
            return False

    async def list_states(self, limit: int = 100) -> List[CrawlState]:
        """列出所有状态"""
        states = []

        try:
            for file_path in self.storage_path.glob("*.json"):
                if len(states) >= limit:
                    break

                task_id = file_path.stem
                state = await self.load_state(task_id)

                if state:
                    states.append(state)

        except Exception as e:
            logger.error(f"列出状态失败: {e}")

        return states

    async def cleanup_old_states(self, days: int = 30) -> int:
        """清理旧状态"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            cleaned_count = 0

            for file_path in self.storage_path.glob("*.json"):
                try:
                    task_id = file_path.stem
                    state = await self.load_state(task_id)

                    if state and state.updated_time < cutoff_time:
                        await self.delete_state(task_id)
                        cleaned_count += 1

                except Exception as e:
                    logger.warning(f"清理状态时出错 {task_id}: {e}")
                    continue

            logger.info(f"清理了 {cleaned_count} 个旧状态")
            return cleaned_count

        except Exception as e:
            logger.error(f"清理状态失败: {e}")
            return 0


class StatefulCrawlConfig(BaseModel):
    """带状态的爬取配置"""

    # 状态管理配置
    enable_state_management: bool = Field(False, description="是否启用状态管理")
    task_id: Optional[str] = Field(None, description="任务ID")
    incremental_strategy: str = Field("time", description="增量策略: time/id/title/custom/content_hash")
    incremental_field: Optional[str] = Field(None, description="增量判断字段名")
    time_format: str = Field("%Y-%m-%d %H:%M:%S", description="时间格式")

    # 状态更新配置
    auto_update_state: bool = Field(True, description="是否自动更新状态")
    save_state_interval: int = Field(10, description="状态保存间隔(文章数)")

    # 增量爬取配置
    max_incremental_pages: int = Field(5, description="增量爬取最大页数")
    stop_on_duplicate: bool = Field(True, description="遇到重复内容时是否停止")
    duplicate_threshold: int = Field(3, description="重复内容阈值")


# 全局状态管理器实例
_state_manager = None


def get_state_manager() -> StateManager:
    """获取状态管理器实例"""
    global _state_manager
    if _state_manager is None:
        _state_manager = StateManager()
    return _state_manager


# 便捷函数
async def save_crawl_state(task_id: str, url: str, **state_data) -> bool:
    """保存爬取状态"""
    manager = get_state_manager()
    state = await manager.get_or_create_state(task_id, url)
    state.update_state(**state_data)
    return await manager.save_state(state)


async def load_crawl_state(task_id: str) -> Optional[CrawlState]:
    """加载爬取状态"""
    manager = get_state_manager()
    return await manager.load_state(task_id)


async def update_crawl_state(task_id: str, **state_data) -> bool:
    """更新爬取状态"""
    manager = get_state_manager()
    return await manager.update_state(task_id, **state_data)


def generate_task_id(url: str, prefix: str = "crawl") -> str:
    """生成任务ID"""
    url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
    timestamp = int(time.time())
    return f"{prefix}_{url_hash}_{timestamp}"
