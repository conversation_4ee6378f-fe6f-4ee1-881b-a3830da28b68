"""
爬取模块测试脚本

用于测试通用文章爬取功能的各种场景
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.crawler import (
    crawl_articles, CrawlConfig, ExtractionStrategy, 
    get_template, ArticleData, PaginationInfo
)


async def test_basic_crawl():
    """测试基础爬取功能"""
    print("🧪 测试基础爬取功能...")
    
    # 使用一个简单的测试页面
    test_script = '''
# 简单的测试脚本
try:
    # 查找所有链接作为文章
    links = soup.select("a")[:5]  # 只取前5个链接
    
    for i, link in enumerate(links):
        title = link.get_text().strip()
        url = link.get("href", "")
        
        if title and url:
            # 处理相对URL
            if not url.startswith("http"):
                url = urljoin(response_data.get("url", ""), url)
            
            article = ArticleData(
                title=f"测试文章 {i+1}: {title[:50]}",
                url=url,
                content=f"这是第{i+1}篇测试文章的内容"
            )
            articles.append(article)
            
except Exception as e:
    print(f"脚本执行错误: {e}")
    '''
    
    config = CrawlConfig(
        url="https://httpbin.org/html",  # 使用httpbin作为测试
        extraction_strategy=ExtractionStrategy.CUSTOM_SCRIPT,
        custom_script=test_script,
        request_delay=0.5,
        timeout=10
    )
    
    try:
        result = await crawl_articles(config)
        
        if result.success:
            print(f"✅ 基础爬取测试成功！")
            print(f"   提取到 {len(result.articles)} 篇文章")
            print(f"   执行时间: {result.execution_time:.2f}秒")
            
            for i, article in enumerate(result.articles[:3], 1):
                print(f"   {i}. {article.title}")
                print(f"      URL: {article.url}")
        else:
            print(f"❌ 基础爬取测试失败: {result.error_message}")
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
    
    print("-" * 50)


async def test_css_selector_crawl():
    """测试CSS选择器爬取"""
    print("🧪 测试CSS选择器爬取...")
    
    config = CrawlConfig(
        url="https://httpbin.org/html",
        extraction_strategy=ExtractionStrategy.CSS_SELECTOR,
        css_selectors={
            "container": "body",  # 使用body作为容器
            "title": "h1",        # 查找h1标签作为标题
            "link": "a",          # 查找链接
            "content": "p"        # 查找段落作为内容
        },
        request_delay=0.5,
        timeout=10
    )
    
    try:
        result = await crawl_articles(config)
        
        if result.success:
            print(f"✅ CSS选择器测试成功！")
            print(f"   提取到 {len(result.articles)} 篇文章")
            
            for i, article in enumerate(result.articles[:2], 1):
                print(f"   {i}. {article.title}")
                print(f"      内容: {article.content[:100]}...")
        else:
            print(f"❌ CSS选择器测试失败: {result.error_message}")
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
    
    print("-" * 50)


async def test_template_usage():
    """测试模板使用"""
    print("🧪 测试脚本模板...")
    
    # 获取基础模板
    basic_template = get_template("basic")
    print(f"✅ 成功获取基础模板，长度: {len(basic_template)} 字符")
    
    # 获取新闻模板
    news_template = get_template("news")
    print(f"✅ 成功获取新闻模板，长度: {len(news_template)} 字符")
    
    print("-" * 50)


async def test_script_security():
    """测试脚本安全性"""
    print("🧪 测试脚本安全性...")
    
    # 测试危险脚本
    dangerous_script = '''
import os
os.system("echo 'This should not work'")
articles.append(ArticleData(title="Dangerous", url="http://example.com"))
    '''
    
    config = CrawlConfig(
        url="https://httpbin.org/html",
        extraction_strategy=ExtractionStrategy.CUSTOM_SCRIPT,
        custom_script=dangerous_script
    )
    
    try:
        result = await crawl_articles(config)
        
        if not result.success and "危险" in result.error_message:
            print("✅ 安全检查正常工作，危险脚本被阻止")
        else:
            print("❌ 安全检查可能存在问题")
            
    except Exception as e:
        print(f"✅ 安全异常被正确捕获: {str(e)}")
    
    print("-" * 50)


async def test_error_handling():
    """测试错误处理"""
    print("🧪 测试错误处理...")
    
    # 测试无效URL
    config = CrawlConfig(
        url="https://invalid-url-that-does-not-exist.com",
        extraction_strategy=ExtractionStrategy.CUSTOM_SCRIPT,
        custom_script="articles.append(ArticleData(title='Test', url='http://test.com'))",
        timeout=5
    )
    
    try:
        result = await crawl_articles(config)
        
        if not result.success:
            print("✅ 错误处理正常，无效URL被正确处理")
            print(f"   错误信息: {result.error_message}")
        else:
            print("❌ 错误处理可能存在问题")
            
    except Exception as e:
        print(f"✅ 异常被正确捕获: {str(e)}")
    
    print("-" * 50)


async def test_data_cleaning():
    """测试数据清洗"""
    print("🧪 测试数据清洗...")
    
    # 创建包含需要清洗数据的脚本
    cleaning_script = '''
# 创建包含需要清洗的数据
articles.append(ArticleData(
    title="  测试标题\\n\\t  ",  # 包含多余空白
    url="/relative/path",        # 相对URL
    content="<p>HTML内容</p>\\n\\r",  # 包含HTML标签
    author="   作者名   "
))
    '''
    
    config = CrawlConfig(
        url="https://httpbin.org/html",
        extraction_strategy=ExtractionStrategy.CUSTOM_SCRIPT,
        custom_script=cleaning_script,
        timeout=10
    )
    
    try:
        result = await crawl_articles(config)
        
        if result.success and result.articles:
            article = result.articles[0]
            print("✅ 数据清洗测试:")
            print(f"   标题: '{article.title}'")
            print(f"   URL: '{article.url}'")
            print(f"   内容: '{article.content}'")
            print(f"   作者: '{article.author}'")
            
            # 检查清洗效果
            if article.title.strip() == "测试标题" and "httpbin.org" in article.url:
                print("✅ 数据清洗工作正常")
            else:
                print("❌ 数据清洗可能存在问题")
        else:
            print(f"❌ 数据清洗测试失败: {result.error_message}")
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
    
    print("-" * 50)


async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行爬取模块测试...")
    print("=" * 60)
    
    tests = [
        ("基础爬取功能", test_basic_crawl),
        ("CSS选择器爬取", test_css_selector_crawl),
        ("模板使用", test_template_usage),
        ("脚本安全性", test_script_security),
        ("错误处理", test_error_handling),
        ("数据清洗", test_data_cleaning),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        try:
            print(f"\n🔄 运行测试: {name}")
            await test_func()
            passed += 1
        except Exception as e:
            print(f"❌ 测试 {name} 运行失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"✨ 测试完成！通过 {passed}/{total} 个测试")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
    else:
        print("⚠️  部分测试失败，请检查实现")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(run_all_tests())
