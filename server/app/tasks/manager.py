"""
任务管理器

统一管理任务的创建、调度、执行和监控
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime

from loguru import logger

from app.models.task import Task, TaskExecution, TaskType, TaskStatus
from .executor import TaskExecutor
from .scheduler import TaskScheduler
from .types import TaskConfig, TriggerType, TaskResult


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.executor = TaskExecutor()
        self.scheduler = TaskScheduler(self.executor)
        self._initialized = False
        self._cleanup_task: Optional[asyncio.Task] = None
    
    async def initialize(self):
        """初始化任务管理器"""
        if self._initialized:
            return

        await self.scheduler.initialize()

        # 启动定期清理任务
        self._cleanup_task = asyncio.create_task(self._periodic_cleanup())

        self._initialized = True
        logger.info("任务管理器已初始化")
    
    async def shutdown(self):
        """关闭任务管理器"""
        if self._initialized:
            # 停止定期清理任务
            if self._cleanup_task and not self._cleanup_task.done():
                self._cleanup_task.cancel()
                try:
                    await self._cleanup_task
                except asyncio.CancelledError:
                    pass

            # 取消所有正在运行的任务
            running_tasks = self.executor.get_running_tasks()
            background_tasks = self.executor.get_background_tasks()

            if running_tasks:
                logger.info(f"正在取消 {len(running_tasks)} 个运行中的任务")
                for task_id in list(running_tasks.keys()):
                    await self.executor.cancel_task(task_id)

            if background_tasks:
                logger.info(f"正在取消 {len(background_tasks)} 个后台任务")
                for execution_id in list(background_tasks.keys()):
                    await self.executor.cancel_background_task(execution_id)

            await self.scheduler.shutdown()
            self._initialized = False
            logger.info("任务管理器已关闭")
    
    async def create_task(
        self, 
        config: TaskConfig, 
    ) -> Task:
        """创建新任务"""
        # 验证任务配置
        await self._validate_task_config(config)
        
        # 创建任务记录
        task = await Task.create(
            name=config.name,
            description=config.description,
            task_type=self._determine_task_type(config.schedule_config),
            module_path=config.module_path,
            function_name=config.function_name,
            parameters=config.parameters,
            schedule_config=config.schedule_config,
            is_active=config.is_active,
        )
        
        # 如果任务启用且不是手动任务，添加到调度器
        if task.is_active and task.task_type != TaskType.MANUAL:
            await self.scheduler.add_task(task)
        
        logger.info(f"任务已创建: {task.name} (ID: {task.id})")
        return task
    
    async def update_task(
        self, 
        task_id: int, 
        config: TaskConfig
    ) -> Task:
        """更新任务"""
        task = await Task.get(id=task_id)
        
        # 验证任务配置
        await self._validate_task_config(config)
        
        # 更新任务字段
        task.name = config.name
        task.description = config.description
        task.task_type = self._determine_task_type(config.schedule_config)
        task.module_path = config.module_path
        task.function_name = config.function_name
        task.parameters = config.parameters
        task.schedule_config = config.schedule_config
        task.is_active = config.is_active
        
        await task.save()
        
        # 更新调度器中的任务
        await self.scheduler.update_task(task)
        
        logger.info(f"任务已更新: {task.name} (ID: {task.id})")
        return task
    
    async def delete_task(self, task_id: int) -> bool:
        """删除任务"""
        task = await Task.get(id=task_id)

        # 检查任务是否正在运行
        if self.executor.is_task_running(task_id):
            # 尝试取消正在运行的任务
            cancelled = await self.executor.cancel_task(task_id)
            if not cancelled:
                raise ValueError(f"任务 {task.name} 正在运行中，无法删除。请先停止任务。")
            logger.info(f"已取消正在运行的任务: {task.name} (ID: {task_id})")

        # 从调度器移除
        await self.scheduler.remove_task(task_id)

        # 删除任务记录
        await task.delete()

        logger.info(f"任务已删除: {task.name} (ID: {task_id})")
        return True
    
    async def enable_task(self, task_id: int) -> bool:
        """启用任务"""
        task = await Task.get(id=task_id)
        task.is_active = True
        await task.save(update_fields=["is_active"])
        
        # 添加到调度器
        if task.task_type != TaskType.MANUAL:
            await self.scheduler.add_task(task)
        
        logger.info(f"任务已启用: {task.name} (ID: {task_id})")
        return True
    
    async def disable_task(self, task_id: int) -> bool:
        """禁用任务"""
        task = await Task.get(id=task_id)
        task.is_active = False
        await task.save(update_fields=["is_active"])
        
        # 从调度器移除
        await self.scheduler.remove_task(task_id)
        
        logger.info(f"任务已禁用: {task.name} (ID: {task_id})")
        return True
    
    async def execute_task_manually(
        self,
        task_id: int,
        parameters: Optional[Dict[str, Any]] = None
    ) -> TaskResult:
        """手动执行任务（同步等待结果）"""
        task = await Task.get(id=task_id)

        # 检查任务是否正在运行（使用实时状态）
        if self.executor.is_task_running(task_id):
            status = self.executor.get_task_status(task_id)
            raise ValueError(f"任务 {task.name} 正在运行中（状态: {status}），无法重复执行")

        logger.info(f"手动执行任务: {task.name} (ID: {task_id})")
        return await self.executor.execute_task(task, TriggerType.MANUAL, parameters)

    async def execute_task_async(
        self,
        task_id: int,
        parameters: Optional[Dict[str, Any]] = None
    ) -> int:
        """异步执行任务（立即返回执行记录ID）"""
        task = await Task.get(id=task_id)

        # 检查任务是否正在运行（使用实时状态）
        if self.executor.is_task_running(task_id):
            status = self.executor.get_task_status(task_id)
            raise ValueError(f"任务 {task.name} 正在运行中（状态: {status}），无法重复执行")

        logger.info(f"异步执行任务: {task.name} (ID: {task_id})")
        return await self.executor.execute_task_async(task, TriggerType.MANUAL, parameters)
    
    async def cancel_task_execution(self, task_id: int) -> bool:
        """取消任务执行"""
        return await self.executor.cancel_task(task_id)

    async def cancel_background_execution(self, execution_id: int) -> bool:
        """取消后台任务执行"""
        return await self.executor.cancel_background_task(execution_id)
    
    async def pause_task(self, task_id: int) -> bool:
        """暂停任务调度"""
        return await self.scheduler.pause_task(task_id)
    
    async def resume_task(self, task_id: int) -> bool:
        """恢复任务调度"""
        return await self.scheduler.resume_task(task_id)
    
    async def get_task_list(
        self,
        page: int = 1,
        page_size: int = 20,
        name_filter: Optional[str] = None,
        status_filter: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取任务列表"""
        query = Task.all()

        if name_filter:
            query = query.filter(name__icontains=name_filter)

        if status_filter:
            if status_filter == "active":
                query = query.filter(is_active=True)
            elif status_filter == "inactive":
                query = query.filter(is_active=False)
            # running状态过滤将在后面通过实时状态处理

        # 分页
        total = await query.count()
        offset = (page - 1) * page_size
        tasks = await query.offset(offset).limit(page_size)

        # 获取实时运行状态和暂停状态
        running_tasks = self.executor.get_running_tasks()

        # 为每个任务添加实时状态
        task_list = []
        for task in tasks:
            task_dict = task.__dict__.copy()

            # 获取实时运行状态
            is_running = task.id in running_tasks
            task_dict['is_running'] = is_running

            # 获取实时暂停状态（只有启用的非手动任务才需要检查暂停状态）
            if task.is_active and task.task_type != 'manual':
                task_dict['is_paused'] = self.scheduler.is_task_paused(task.id)
            else:
                task_dict['is_paused'] = False

            # 如果有running状态过滤，在这里处理
            if status_filter == "running" and not is_running:
                continue

            task_list.append(task_dict)

        return {
            "items": task_list,
            "total": total,
            "page": page,
            "page_size": page_size
        }
    
    async def get_task_detail(self, task_id: int) -> Dict[str, Any]:
        """获取任务详情"""
        task = await Task.get(id=task_id)
        task_dict = task.__dict__.copy()

        # 获取实时运行状态
        running_tasks = self.executor.get_running_tasks()
        task_dict['is_running'] = task_id in running_tasks

        # 获取实时暂停状态（只有启用的非手动任务才需要检查暂停状态）
        if task.is_active and task.task_type != 'manual':
            task_dict['is_paused'] = self.scheduler.is_task_paused(task_id)
        else:
            task_dict['is_paused'] = False

        return task_dict
    
    async def get_task_executions(
        self, 
        task_id: int, 
        page: int = 1, 
        page_size: int = 20
    ) -> Dict[str, Any]:
        """获取任务执行历史"""
        query = TaskExecution.filter(task_id=task_id)
        
        total = await query.count()
        offset = (page - 1) * page_size
        executions = await query.offset(offset).limit(page_size).order_by("-id")
        
        return {
            "items": executions,
            "total": total,
            "page": page,
            "page_size": page_size
        }
    
    async def get_task_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        total_tasks = await Task.all().count()
        active_tasks = await Task.filter(is_active=True).count()

        # 获取实时运行任务数量
        running_tasks_dict = self.executor.get_running_tasks()
        running_tasks = len(running_tasks_dict)
        
        # 最近24小时执行统计
        from datetime import timedelta
        yesterday = datetime.now() - timedelta(days=1)
        
        recent_executions = await TaskExecution.filter(created_at__gte=yesterday).count()
        recent_success = await TaskExecution.filter(
            created_at__gte=yesterday, 
            status=TaskStatus.SUCCESS
        ).count()
        recent_failed = await TaskExecution.filter(
            created_at__gte=yesterday, 
            status=TaskStatus.FAILED
        ).count()
        
        return {
            "total_tasks": total_tasks,
            "active_tasks": active_tasks,
            "running_tasks": running_tasks,
            "recent_executions": recent_executions,
            "recent_success": recent_success,
            "recent_failed": recent_failed,
            "success_rate": recent_success / recent_executions if recent_executions > 0 else 0
        }
    
    def get_running_tasks(self) -> Dict[int, str]:
        """获取正在运行的任务"""
        return self.executor.get_running_tasks()

    def get_background_tasks(self) -> Dict[int, str]:
        """获取后台任务"""
        return self.executor.get_background_tasks()

    def get_scheduler_info(self) -> Dict[int, Dict]:
        """获取调度器信息"""
        return self.scheduler.get_all_jobs()

    async def cleanup_completed_tasks(self):
        """清理已完成的任务"""
        await self.executor.cleanup_completed_tasks()

    def get_task_runtime_status(self, task_id: int) -> Dict[str, Any]:
        """获取任务的运行时状态"""
        status = self.executor.get_task_status(task_id)
        is_running = self.executor.is_task_running(task_id)

        result = {
            "is_running": is_running,
            "status": status,
            "in_running_tasks": task_id in self.executor.running_tasks,
            "in_background_tasks": task_id in self.executor.task_execution_map
        }

        # 如果是后台任务，添加执行ID
        if task_id in self.executor.task_execution_map:
            result["execution_id"] = self.executor.task_execution_map[task_id]

        return result

    async def _periodic_cleanup(self):
        """定期清理已完成的任务"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟清理一次
                await self.cleanup_completed_tasks()
            except asyncio.CancelledError:
                logger.info("定期清理任务已停止")
                break
            except Exception as e:
                logger.error(f"定期清理任务异常: {e}")

    async def _validate_task_config(self, config: TaskConfig):
        """验证任务配置"""
        if not config.name:
            raise ValueError("任务名称不能为空")
        
        if not config.module_path:
            raise ValueError("模块路径不能为空")
        
        if not config.function_name:
            raise ValueError("函数名不能为空")
        
        # 验证模块和函数是否存在
        try:
            import importlib
            module = importlib.import_module(config.module_path)
            if not hasattr(module, config.function_name):
                raise ValueError(f"模块 {config.module_path} 中未找到函数 {config.function_name}")
        except ImportError:
            raise ValueError(f"无法导入模块: {config.module_path}")
    
    def _determine_task_type(self, schedule_config: Dict[str, Any]) -> str:
        """根据调度配置确定任务类型"""
        if not schedule_config:
            return TaskType.MANUAL
        
        if "expression" in schedule_config:
            return TaskType.CRON
        elif any(key in schedule_config for key in ["seconds", "minutes", "hours", "days"]):
            return TaskType.INTERVAL
        elif "run_date" in schedule_config:
            return TaskType.DATE
        else:
            return TaskType.MANUAL
