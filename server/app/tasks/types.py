"""
任务系统类型定义
"""

from typing import Any, Dict, Optional, Callable, Awaitable, Union
from datetime import datetime
from dataclasses import dataclass
from enum import StrEnum

# 任务函数类型定义
TaskFunction = Callable[..., Awaitable[Any]]


@dataclass
class TaskResult:
    """任务执行结果"""
    success: bool
    result: Any = None
    error: Optional[str] = None
    traceback: Optional[str] = None
    duration: Optional[float] = None
    started_at: Optional[datetime] = None
    finished_at: Optional[datetime] = None


@dataclass
class TaskConfig:
    """任务配置"""
    name: str
    description: Optional[str] = None
    module_path: str = ""
    function_name: str = ""
    parameters: Dict[str, Any] = None
    schedule_config: Dict[str, Any] = None
    is_active: bool = True
    max_instances: int = 1
    coalesce: bool = True
    misfire_grace_time: int = 30
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}
        if self.schedule_config is None:
            self.schedule_config = {}


class TriggerType(StrEnum):
    """触发类型"""
    SCHEDULED = "scheduled"  # 调度触发
    MANUAL = "manual"       # 手动触发
    API = "api"            # API触发
    DEPENDENCY = "dependency"  # 依赖触发


@dataclass
class CronSchedule:
    """Cron调度配置"""
    expression: str
    timezone: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


@dataclass
class IntervalSchedule:
    """间隔调度配置"""
    seconds: int = 0
    minutes: int = 0
    hours: int = 0
    days: int = 0
    weeks: int = 0
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


@dataclass
class DateSchedule:
    """日期调度配置"""
    run_date: datetime
    timezone: Optional[str] = None


# 调度配置联合类型
ScheduleConfig = Union[CronSchedule, IntervalSchedule, DateSchedule]


@dataclass
class TaskExecutionContext:
    """任务执行上下文"""
    task_id: int
    execution_id: int
    trigger_type: TriggerType
    parameters: Dict[str, Any]
    started_at: datetime
    worker_id: str
