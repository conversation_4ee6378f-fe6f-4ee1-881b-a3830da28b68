"""
任务执行器

负责具体任务的执行逻辑
"""

import asyncio
import importlib
import traceback
import uuid
from datetime import UTC, datetime
from typing import Any, Dict, Optional

from loguru import logger

from app.models.task import Task, TaskExecution, TaskStatus
from .types import TaskResult, TaskExecutionContext, TriggerType


class TaskExecutor:
    """任务执行器"""

    def __init__(self):
        self.worker_id = str(uuid.uuid4())[:8]
        self.running_tasks: Dict[int, asyncio.Task] = {}  # task_id -> asyncio.Task 映射
        self.background_tasks: Dict[int, asyncio.Task] = {}  # execution_id -> asyncio.Task 映射
        self.task_execution_map: Dict[int, int] = {}  # task_id -> execution_id 映射（用于后台任务）
    
    async def execute_task(
        self,
        task: Task,
        trigger_type: TriggerType = TriggerType.SCHEDULED,
        parameters: Optional[Dict[str, Any]] = None
    ) -> TaskResult:
        """执行任务（同步等待结果）"""
        if parameters is None:
            parameters = {}

        # 检查任务是否已在运行
        if task.id in self.running_tasks:
            raise ValueError(f"任务 {task.name} (ID: {task.id}) 已在运行中")

        # 合并任务默认参数和传入参数
        merged_params = {**task.parameters, **parameters}

        # 创建执行记录
        execution = await TaskExecution.create(
            task=task,
            status=TaskStatus.PENDING,
            trigger_type=trigger_type,
            parameters=merged_params,
            worker_id=self.worker_id
        )

        # 创建执行上下文
        context = TaskExecutionContext(
            task_id=task.id,
            execution_id=execution.id,
            trigger_type=trigger_type,
            parameters=merged_params,
            started_at=datetime.now(),
            worker_id=self.worker_id
        )

        logger.info(f"开始执行任务: {task.name} (ID: {task.id})")

        # 创建任务并添加到运行任务字典
        task_coroutine = self._execute_task_internal(task, execution, context)
        running_task = asyncio.create_task(task_coroutine)
        self.running_tasks[task.id] = running_task

        try:
            # 等待任务完成
            result = await running_task
            logger.info(f"任务执行完成: {task.name} - {'成功' if result.success else '失败'}")
            return result

        except asyncio.CancelledError:
            logger.warning(f"任务被取消: {task.name} (ID: {task.id})")
            # 更新执行记录为已取消
            await self._update_execution_status(
                execution,
                TaskStatus.CANCELLED,
                finished_at=datetime.now()
            )
            raise

        except Exception as e:
            error_msg = str(e)
            error_traceback = traceback.format_exc()
            logger.error(f"任务执行异常: {task.name} - {error_msg}")

            # 更新执行记录为失败
            await self._update_execution_status(
                execution,
                TaskStatus.FAILED,
                finished_at=datetime.now(),
                error_message=error_msg,
                traceback=error_traceback
            )

            # 更新任务统计
            await self._update_task_stats(task, False)

            return TaskResult(
                success=False,
                error=error_msg,
                traceback=error_traceback,
                started_at=context.started_at,
                finished_at=datetime.now()
            )

        finally:
            # 从运行任务字典中移除
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
    
    async def _execute_task_internal(
        self,
        task: Task,
        execution: TaskExecution,
        context: TaskExecutionContext
    ) -> TaskResult:
        """内部任务执行方法"""
        try:
            # 更新任务状态为运行中
            await self._update_task_running_status(task, True)
            await self._update_execution_status(execution, TaskStatus.RUNNING, started_at=datetime.now())

            # 执行任务
            result = await self._run_task_function(task, context)

            # 更新执行记录
            await self._update_execution_status(
                execution,
                TaskStatus.SUCCESS if result.success else TaskStatus.FAILED,
                finished_at=datetime.now(),
                duration=result.duration,
                result=result.result,
                error_message=result.error,
                traceback=result.traceback
            )

            # 更新任务统计
            await self._update_task_stats(task, result.success)

            return result

        except Exception as e:
            error_msg = str(e)
            error_traceback = traceback.format_exc()

            # 更新执行记录为失败
            await self._update_execution_status(
                execution,
                TaskStatus.FAILED,
                finished_at=datetime.now(),
                error_message=error_msg,
                traceback=error_traceback
            )

            # 更新任务统计
            await self._update_task_stats(task, False)

            return TaskResult(
                success=False,
                error=error_msg,
                traceback=error_traceback,
                started_at=context.started_at,
                finished_at=datetime.now()
            )

        finally:
            # 更新任务状态为非运行中
            await self._update_task_running_status(task, False)

    async def _run_task_function(self, task: Task, context: TaskExecutionContext) -> TaskResult:
        """运行任务函数"""
        started_at = datetime.now()
        
        try:
            # 动态导入模块
            module = importlib.import_module(task.module_path)
            
            # 获取函数
            if not hasattr(module, task.function_name):
                raise AttributeError(f"模块 {task.module_path} 中未找到函数 {task.function_name}")
            
            func = getattr(module, task.function_name)
            
            # 执行函数
            if asyncio.iscoroutinefunction(func):
                result = await func(**context.parameters)
            else:
                result = func(**context.parameters)
            
            finished_at = datetime.now()
            duration = (finished_at - started_at).total_seconds()
            
            return TaskResult(
                success=True,
                result=result,
                duration=duration,
                started_at=started_at,
                finished_at=finished_at
            )
            
        except Exception as e:
            finished_at = datetime.now()
            duration = (finished_at - started_at).total_seconds()
            
            return TaskResult(
                success=False,
                error=str(e),
                traceback=traceback.format_exc(),
                duration=duration,
                started_at=started_at,
                finished_at=finished_at
            )
    
    async def _update_task_running_status(self, task: Task, is_running: bool):
        """更新任务运行状态"""
        # 运行状态不再写入数据库，只更新last_run_at
        if not is_running:
            task.last_run_at = datetime.now()
            await task.save(update_fields=["last_run_at"])
    
    async def _update_execution_status(
        self, 
        execution: TaskExecution, 
        status: TaskStatus,
        **kwargs
    ):
        """更新执行记录状态"""
        execution.status = status
        for key, value in kwargs.items():
            if hasattr(execution, key):
                setattr(execution, key, value)
        await execution.save()
    
    async def _update_task_stats(self, task: Task, success: bool):
        """更新任务统计信息"""
        task.total_runs += 1
        if success:
            task.success_runs += 1
        else:
            task.failed_runs += 1
        await task.save(update_fields=["total_runs", "success_runs", "failed_runs"])
    
    async def execute_task_async(
        self,
        task: Task,
        trigger_type: TriggerType = TriggerType.MANUAL,
        parameters: Optional[Dict[str, Any]] = None
    ) -> int:
        """异步执行任务，立即返回执行记录ID"""
        if parameters is None:
            parameters = {}

        # 检查任务是否已在运行（包括同步和异步）
        if task.id in self.running_tasks:
            raise ValueError(f"任务 {task.name} (ID: {task.id}) 已在运行中")

        # 检查是否已有该任务的后台执行
        if task.id in self.task_execution_map:
            execution_id = self.task_execution_map[task.id]
            if execution_id in self.background_tasks:
                raise ValueError(f"任务 {task.name} (ID: {task.id}) 已在后台执行中")

        # 合并任务默认参数和传入参数
        merged_params = {**task.parameters, **parameters}

        # 创建执行记录
        execution = await TaskExecution.create(
            task=task,
            status=TaskStatus.PENDING,
            trigger_type=trigger_type,
            parameters=merged_params,
            worker_id=self.worker_id
        )

        # 创建执行上下文
        context = TaskExecutionContext(
            task_id=task.id,
            execution_id=execution.id,
            trigger_type=trigger_type,
            parameters=merged_params,
            started_at=datetime.now(),
            worker_id=self.worker_id
        )

        # 创建后台任务
        background_task = asyncio.create_task(
            self._execute_task_background(task, execution, context)
        )

        # 记录后台任务和映射关系
        self.background_tasks[execution.id] = background_task
        self.task_execution_map[task.id] = execution.id

        # 添加完成回调，自动清理
        background_task.add_done_callback(
            lambda t: self._cleanup_background_task(execution.id, task.id)
        )

        logger.info(f"任务已添加到后台执行队列: {task.name} (ID: {task.id}, 执行ID: {execution.id})")
        return execution.id

    def _cleanup_background_task(self, execution_id: int, task_id: int):
        """清理完成的后台任务"""
        try:
            # 从后台任务映射中移除
            if execution_id in self.background_tasks:
                del self.background_tasks[execution_id]

            # 从任务执行映射中移除
            if task_id in self.task_execution_map and self.task_execution_map[task_id] == execution_id:
                del self.task_execution_map[task_id]

            logger.debug(f"已清理后台任务: 执行ID {execution_id}, 任务ID {task_id}")
        except Exception as e:
            logger.error(f"清理后台任务失败: {e}")

    async def _execute_task_background(
        self,
        task: Task,
        execution: TaskExecution,
        context: TaskExecutionContext
    ):
        """后台执行任务的内部方法"""
        logger.info(f"开始后台执行任务: {task.name} (ID: {task.id})")

        try:
            # 执行任务（使用内部执行方法）
            result = await self._execute_task_internal(task, execution, context)
            logger.info(f"后台任务执行完成: {task.name} - {'成功' if result.success else '失败'}")
            return result

        except asyncio.CancelledError:
            logger.warning(f"后台任务被取消: {task.name} (ID: {task.id})")
            # 更新执行记录为已取消
            await self._update_execution_status(
                execution,
                TaskStatus.CANCELLED,
                finished_at=datetime.now()
            )
            raise

        except Exception as e:
            error_msg = str(e)
            error_traceback = traceback.format_exc()
            logger.error(f"后台任务执行异常: {task.name} - {error_msg}")

            # 更新执行记录为失败
            await self._update_execution_status(
                execution,
                TaskStatus.FAILED,
                finished_at=datetime.now(),
                error_message=error_msg,
                traceback=error_traceback
            )

            # 更新任务统计
            await self._update_task_stats(task, False)

            return TaskResult(
                success=False,
                error=error_msg,
                traceback=error_traceback,
                started_at=context.started_at,
                finished_at=datetime.now()
            )

    async def cancel_task(self, task_id: int) -> bool:
        """取消正在运行的任务（同步执行的任务）"""
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            task.cancel()
            logger.info(f"已取消同步任务: {task_id}")
            return True

        # 检查是否有该任务的后台执行
        if task_id in self.task_execution_map:
            execution_id = self.task_execution_map[task_id]
            return await self.cancel_background_task(execution_id)

        return False

    async def cancel_background_task(self, execution_id: int) -> bool:
        """取消后台执行的任务"""
        if execution_id in self.background_tasks:
            task = self.background_tasks[execution_id]
            task.cancel()

            # 更新执行记录状态为已取消
            try:
                execution = await TaskExecution.get(id=execution_id)
                await self._update_execution_status(execution, TaskStatus.CANCELLED, finished_at=datetime.now())
                logger.info(f"已取消后台任务: 执行ID {execution_id}")
                return True
            except Exception as e:
                logger.error(f"取消后台任务时更新状态失败: {e}")
                return False
        return False

    def get_running_tasks(self) -> Dict[int, str]:
        """获取正在运行的任务列表 - 返回 task_id -> status 映射"""
        result = {}

        # 添加同步运行的任务
        for task_id in self.running_tasks.keys():
            result[task_id] = "running"

        # 添加后台运行的任务
        for task_id, execution_id in self.task_execution_map.items():
            if execution_id in self.background_tasks:
                result[task_id] = "background"

        return result

    def get_background_tasks(self) -> Dict[int, str]:
        """获取后台任务列表 - 返回 execution_id -> status 映射"""
        return {execution_id: "background" for execution_id in self.background_tasks.keys()}

    def is_task_running(self, task_id: int) -> bool:
        """检查任务是否正在运行"""
        return (task_id in self.running_tasks or
                task_id in self.task_execution_map)

    async def cleanup_completed_tasks(self):
        """清理已完成的任务（定期维护方法）"""
        completed_executions = []

        # 检查后台任务是否完成
        for execution_id, task in list(self.background_tasks.items()):
            if task.done():
                completed_executions.append(execution_id)

        # 清理已完成的任务
        for execution_id in completed_executions:
            # 找到对应的task_id
            task_id = None
            for tid, eid in list(self.task_execution_map.items()):
                if eid == execution_id:
                    task_id = tid
                    break

            if task_id:
                self._cleanup_background_task(execution_id, task_id)

        if completed_executions:
            logger.debug(f"清理了 {len(completed_executions)} 个已完成的后台任务")

    def get_task_status(self, task_id: int) -> Optional[str]:
        """获取任务的运行状态"""
        if task_id in self.running_tasks:
            return "running"
        elif task_id in self.task_execution_map:
            execution_id = self.task_execution_map[task_id]
            if execution_id in self.background_tasks:
                return "background"
        return None
