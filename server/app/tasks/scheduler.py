"""
任务调度器

基于APScheduler的任务调度管理
"""

from typing import Dict, Optional

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.date import DateTrigger
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor
from loguru import logger

from app.models.task import Task, TaskType
from .executor import TaskExecutor
from .types import TriggerType


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self, executor: TaskExecutor):
        self.executor = executor
        self.scheduler: Optional[AsyncIOScheduler] = None
        self.job_map: Dict[int, str] = {}  # task_id -> job_id 映射
        
    async def initialize(self):
        """初始化调度器"""
        if self.scheduler is not None:
            return
            
        # 配置调度器
        jobstores = {
            'default': MemoryJobStore()
        }
        executors = {
            'default': AsyncIOExecutor()
        }
        job_defaults = {
            'coalesce': True,
            'max_instances': 1,
            'misfire_grace_time': 30
        }
        
        self.scheduler = AsyncIOScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Shanghai'
        )
        
        # 启动调度器
        self.scheduler.start()
        logger.info("任务调度器已启动")
        
        # 加载现有任务
        await self._load_existing_tasks()
    
    async def shutdown(self):
        """关闭调度器"""
        if self.scheduler:
            self.scheduler.shutdown(wait=True)
            self.scheduler = None
            logger.info("任务调度器已关闭")
    
    async def add_task(self, task: Task) -> bool:
        """添加任务到调度器"""
        if not self.scheduler:
            logger.error("调度器未初始化")
            return False
            
        if not task.is_active:
            logger.info(f"任务 {task.name} 未启用，跳过调度")
            return False
            
        try:
            # 根据任务类型创建触发器
            trigger = await self._create_trigger(task)
            if not trigger:
                logger.warning(f"无法为任务 {task.name} 创建触发器")
                return False
            
            # 添加任务到调度器
            job_id = f"task_{task.id}"
            self.scheduler.add_job(
                func=self._execute_scheduled_task,
                trigger=trigger,
                id=job_id,
                args=[task.id],
                name=task.name,
                max_instances=1,
                coalesce=True,
                misfire_grace_time=30
            )
            
            # 记录映射关系
            self.job_map[task.id] = job_id
            
            # 更新下次执行时间
            job = self.scheduler.get_job(job_id)
            if job and job.next_run_time:
                task.next_run_at = job.next_run_time
                await task.save(update_fields=["next_run_at"])
            
            logger.info(f"任务 {task.name} 已添加到调度器")
            return True
            
        except Exception as e:
            logger.error(f"添加任务到调度器失败: {task.name} - {e}")
            return False
    
    async def remove_task(self, task_id: int) -> bool:
        """从调度器移除任务"""
        if not self.scheduler:
            return False
            
        job_id = self.job_map.get(task_id)
        if job_id:
            try:
                self.scheduler.remove_job(job_id)
                del self.job_map[task_id]
                logger.info(f"任务 {task_id} 已从调度器移除")
                return True
            except Exception as e:
                logger.error(f"从调度器移除任务失败: {task_id} - {e}")
        return False
    
    async def update_task(self, task: Task) -> bool:
        """更新调度器中的任务"""
        # 先移除旧任务
        await self.remove_task(task.id)
        
        # 重新添加任务
        if task.is_active:
            return await self.add_task(task)
        return True
    
    async def pause_task(self, task_id: int) -> bool:
        """暂停任务"""
        job_id = self.job_map.get(task_id)
        if job_id and self.scheduler:
            try:
                self.scheduler.pause_job(job_id)
                logger.info(f"任务 {task_id} 已暂停")
                return True
            except Exception as e:
                logger.error(f"暂停任务失败: {task_id} - {e}")
        return False
    
    async def resume_task(self, task_id: int) -> bool:
        """恢复任务"""
        job_id = self.job_map.get(task_id)
        if job_id and self.scheduler:
            try:
                self.scheduler.resume_job(job_id)
                logger.info(f"任务 {task_id} 已恢复")
                return True
            except Exception as e:
                logger.error(f"恢复任务失败: {task_id} - {e}")
        return False
    
    async def _create_trigger(self, task: Task):
        """根据任务配置创建触发器"""
        task_type = TaskType(task.task_type)
        config = task.schedule_config
        
        if task_type == TaskType.CRON:
            # Cron触发器
            expression = config.get('expression')
            if not expression:
                return None
            return CronTrigger.from_crontab(
                expression,
                timezone=config.get('timezone', 'Asia/Shanghai')
            )
            
        elif task_type == TaskType.INTERVAL:
            # 间隔触发器
            seconds = config.get('seconds', 0)
            minutes = config.get('minutes', 0)
            hours = config.get('hours', 0)
            days = config.get('days', 0)
            
            if not any([seconds, minutes, hours, days]):
                return None
                
            return IntervalTrigger(
                seconds=seconds,
                minutes=minutes,
                hours=hours,
                days=days,
                start_date=config.get('start_date'),
                end_date=config.get('end_date')
            )
            
        elif task_type == TaskType.DATE:
            # 日期触发器
            run_date = config.get('run_date')
            if not run_date:
                return None
            return DateTrigger(
                run_date=run_date,
                timezone=config.get('timezone', 'Asia/Shanghai')
            )
        
        return None
    
    async def _execute_scheduled_task(self, task_id: int):
        """执行调度的任务"""
        try:
            task = await Task.get(id=task_id)

            # 检查任务是否已在运行
            if self.executor.is_task_running(task_id):
                logger.warning(f"任务 {task.name} (ID: {task_id}) 已在运行中，跳过本次调度执行")
                return

            await self.executor.execute_task(task, TriggerType.SCHEDULED)

            # 更新下次执行时间
            job_id = self.job_map.get(task_id)
            if job_id:
                job = self.scheduler.get_job(job_id)
                if job and job.next_run_time:
                    task.next_run_at = job.next_run_time
                    await task.save(update_fields=["next_run_at"])

        except Exception as e:
            logger.error(f"执行调度任务失败: {task_id} - {e}")
    
    async def _load_existing_tasks(self):
        """加载现有的活跃任务"""
        tasks = await Task.filter(is_active=True).all()
        for task in tasks:
            if task.task_type != TaskType.MANUAL:
                await self.add_task(task)
        logger.info(f"已加载 {len(tasks)} 个活跃任务")
    
    def get_job_info(self, task_id: int) -> Optional[Dict]:
        """获取任务的调度信息"""
        job_id = self.job_map.get(task_id)
        if job_id and self.scheduler:
            job = self.scheduler.get_job(job_id)
            if job:
                return {
                    'id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time,
                    'trigger': str(job.trigger)
                }
        return None

    def is_task_paused(self, task_id: int) -> bool:
        """检查任务是否暂停"""
        job_id = self.job_map.get(task_id)
        if job_id and self.scheduler:
            job = self.scheduler.get_job(job_id)
            if job:
                # APScheduler中暂停的任务next_run_time为None
                # 但是一次性任务执行完后也会是None，所以需要检查trigger类型
                # 对于cron和interval任务，如果next_run_time为None且job存在，说明被暂停了
                from apscheduler.triggers.date import DateTrigger
                if isinstance(job.trigger, DateTrigger):
                    # 一次性任务执行完后会被自动移除，如果还存在说明未执行
                    return False
                else:
                    # cron和interval任务如果next_run_time为None说明被暂停了
                    return job.next_run_time is None
        return False

    def get_all_jobs(self) -> Dict[int, Dict]:
        """获取所有任务的调度信息"""
        result = {}
        for task_id, job_id in self.job_map.items():
            info = self.get_job_info(task_id)
            if info:
                result[task_id] = info
        return result
