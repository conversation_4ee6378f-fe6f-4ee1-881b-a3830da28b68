# 动态任务管理系统

这是一个基于FastAPI和APScheduler的动态任务管理系统，支持任务的创建、调度、执行和监控。

## 功能特性

- ✅ **动态任务管理**: 支持运行时创建、更新、删除任务
- ✅ **多种调度方式**: 支持Cron、间隔、一次性任务
- ✅ **任务监控**: 实时查看任务状态和执行历史
- ✅ **错误处理**: 完善的异常捕获和错误记录
- ✅ **任务模板**: 提供常用任务模板，快速创建任务
- ✅ **权限控制**: 基于角色的访问控制
- ✅ **操作日志**: 记录所有任务操作日志

## 系统架构

```
app/tasks/
├── __init__.py          # 模块初始化
├── manager.py           # 任务管理器
├── executor.py          # 任务执行器
├── scheduler.py         # 任务调度器
├── types.py             # 类型定义
├── examples.py          # 示例任务函数
├── templates.py         # 任务模板
└── README.md           # 说明文档

app/models/
└── task.py             # 任务相关数据模型

app/api/tasks/
└── __init__.py         # 任务管理API
```

## 数据模型

### Task (任务定义)
- 任务基本信息：名称、描述、类型
- 执行配置：模块路径、函数名、参数
- 调度配置：调度类型、表达式、间隔等
- 状态管理：是否启用、是否运行中
- 统计信息：执行次数、成功/失败次数

### TaskExecution (任务执行记录)
- 执行信息：状态、触发类型、开始/结束时间
- 执行结果：返回值、错误信息、执行时长
- 执行环境：执行器ID、参数

### TaskSchedule (任务调度配置)
- 调度配置：类型、表达式、间隔、运行日期
- 调度状态：是否启用、调度器任务ID
- 限制配置：最大并发数、合并策略、宽限时间

## API接口

### 任务管理
- `GET /api/tasks` - 获取任务列表
- `GET /api/tasks/{task_id}` - 获取任务详情
- `POST /api/tasks` - 创建任务
- `PUT /api/tasks/{task_id}` - 更新任务
- `DELETE /api/tasks/{task_id}` - 删除任务

### 任务控制
- `POST /api/tasks/{task_id}/enable` - 启用任务
- `POST /api/tasks/{task_id}/disable` - 禁用任务
- `POST /api/tasks/{task_id}/execute` - 手动执行任务
- `POST /api/tasks/{task_id}/cancel` - 取消任务执行
- `POST /api/tasks/{task_id}/pause` - 暂停任务调度
- `POST /api/tasks/{task_id}/resume` - 恢复任务调度

### 任务监控
- `GET /api/tasks/{task_id}/executions` - 获取执行历史
- `GET /api/tasks/statistics/overview` - 获取任务统计

### 辅助接口
- `GET /api/tasks/templates` - 获取任务模板
- `GET /api/tasks/cron-examples` - 获取Cron示例
- `GET /api/tasks/interval-examples` - 获取间隔示例
- `GET /api/tasks/categories` - 获取任务分类

## 使用示例

### 1. 创建简单的定时任务

```python
# 创建任务配置
task_config = {
    "name": "每日数据清理",
    "description": "清理30天前的日志数据",
    "module_path": "app.tasks.examples",
    "function_name": "data_cleanup_task",
    "parameters": {
        "days": 30,
        "table": "logs"
    },
    "schedule_config": {
        "expression": "0 2 * * *"  # 每天凌晨2点
    },
    "is_active": True
}

# 通过API创建任务
POST /api/tasks
Content-Type: application/json
{
    "name": "每日数据清理",
    "description": "清理30天前的日志数据",
    "module_path": "app.tasks.examples",
    "function_name": "data_cleanup_task",
    "parameters": {
        "days": 30,
        "table": "logs"
    },
    "schedule_config": {
        "expression": "0 2 * * *"
    },
    "is_active": true
}
```

### 2. 创建间隔任务

```python
# 健康检查任务，每5分钟执行一次
task_config = {
    "name": "系统健康检查",
    "description": "检查系统各组件状态",
    "module_path": "app.tasks.examples", 
    "function_name": "health_check_task",
    "parameters": {
        "services": ["database", "redis", "api"]
    },
    "schedule_config": {
        "minutes": 5
    },
    "is_active": True
}
```

### 3. 创建一次性任务

```python
# 指定时间执行的备份任务
from datetime import datetime, timedelta

run_time = datetime.now() + timedelta(hours=1)

task_config = {
    "name": "临时备份任务",
    "description": "一小时后执行数据库备份",
    "module_path": "app.tasks.examples",
    "function_name": "backup_database_task", 
    "parameters": {
        "database": "zcws",
        "backup_path": "/data/backups"
    },
    "schedule_config": {
        "run_date": run_time.isoformat()
    },
    "is_active": True
}
```

### 4. 手动执行任务

```python
# 手动执行任务，可以传入自定义参数
POST /api/tasks/1/execute
Content-Type: application/json
{
    "parameters": {
        "days": 7,  # 覆盖默认参数
        "table": "error_logs"
    }
}
```

## 编写自定义任务函数

### 异步任务函数

```python
async def my_async_task(**kwargs) -> dict:
    """
    自定义异步任务函数
    
    Args:
        **kwargs: 任务参数
        
    Returns:
        dict: 任务执行结果
    """
    # 获取参数
    param1 = kwargs.get('param1', 'default_value')
    param2 = kwargs.get('param2', 100)
    
    # 执行任务逻辑
    await asyncio.sleep(1)  # 模拟异步操作
    
    # 返回结果
    return {
        'status': 'success',
        'processed_items': param2,
        'message': f'处理完成: {param1}'
    }
```

### 同步任务函数

```python
def my_sync_task(**kwargs) -> str:
    """
    自定义同步任务函数
    
    Args:
        **kwargs: 任务参数
        
    Returns:
        str: 任务执行结果
    """
    name = kwargs.get('name', 'World')
    return f'Hello, {name}!'
```

## 调度配置说明

### Cron表达式
```python
# 格式: 分 时 日 月 周
{
    "expression": "0 9 * * 1-5"  # 工作日上午9点
}
```

### 间隔调度
```python
{
    "seconds": 30,    # 秒
    "minutes": 5,     # 分钟
    "hours": 1,       # 小时
    "days": 1         # 天数
}
```

### 一次性调度
```python
{
    "run_date": "2024-01-01T10:00:00"  # ISO格式时间
}
```

## 监控和日志

系统提供完善的监控功能：

1. **任务状态监控**: 实时查看任务运行状态
2. **执行历史**: 查看任务的历史执行记录
3. **错误日志**: 详细的错误信息和堆栈跟踪
4. **统计信息**: 任务执行统计和成功率
5. **操作日志**: 记录所有任务管理操作

## 注意事项

1. **权限要求**: 任务管理功能需要超级管理员权限
2. **任务函数**: 任务函数必须接受 `**kwargs` 参数
3. **错误处理**: 任务函数应该处理可能的异常
4. **资源管理**: 长时间运行的任务应该合理使用系统资源
5. **参数验证**: 创建任务时会验证模块和函数是否存在

## 扩展开发

要添加新的任务类型或功能：

1. 在 `app/tasks/examples.py` 中添加任务函数
2. 在 `app/tasks/templates.py` 中添加任务模板
3. 根据需要扩展数据模型和API接口
4. 更新相关文档和测试用例
