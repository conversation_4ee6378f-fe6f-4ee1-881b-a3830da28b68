"""
示例任务函数

提供一些常用的示例任务，用于演示任务系统的功能
"""

import asyncio
import random
from datetime import datetime
from typing import Dict, Any

from loguru import logger


async def hello_world_task(**kwargs) -> str:
    """
    简单的Hello World任务
    """
    name = kwargs.get('name', 'World')
    message = f"Hello, {name}! Current time: {datetime.now()}"
    logger.info(f"执行Hello World任务: {message}")
    return message


async def data_cleanup_task(**kwargs) -> Dict[str, Any]:
    """
    数据清理任务示例
    """
    days = kwargs.get('days', 30)
    table_name = kwargs.get('table', 'logs')
    
    logger.info(f"开始清理 {table_name} 表中 {days} 天前的数据")
    for i in range(15):
        # 模拟数据清理过程
        await asyncio.sleep(2)

    # 模拟清理结果
    deleted_count = random.randint(100, 1000)
    
    result = {
        'table': table_name,
        'days': days,
        'deleted_count': deleted_count,
        'completed_at': datetime.now().isoformat()
    }
    
    logger.info(f"数据清理完成: {result}")
    return result


async def backup_database_task(**kwargs) -> Dict[str, Any]:
    """
    数据库备份任务示例
    """
    database = kwargs.get('database', 'default')
    backup_path = kwargs.get('backup_path', '/data/backups')
    
    logger.info(f"开始备份数据库: {database}")
    
    # 模拟备份过程
    await asyncio.sleep(5)
    
    # 模拟备份结果
    backup_file = f"{backup_path}/{database}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
    file_size = random.randint(1024*1024, 1024*1024*100)  # 1MB - 100MB
    
    result = {
        'database': database,
        'backup_file': backup_file,
        'file_size': file_size,
        'completed_at': datetime.now().isoformat()
    }
    
    logger.info(f"数据库备份完成: {result}")
    return result


async def send_report_task(**kwargs) -> Dict[str, Any]:
    """
    发送报告任务示例
    """
    report_type = kwargs.get('report_type', 'daily')
    recipients = kwargs.get('recipients', ['<EMAIL>'])
    
    logger.info(f"开始生成并发送 {report_type} 报告")
    
    # 模拟报告生成
    await asyncio.sleep(3)
    
    # 模拟发送过程
    await asyncio.sleep(1)
    
    result = {
        'report_type': report_type,
        'recipients': recipients,
        'sent_count': len(recipients),
        'completed_at': datetime.now().isoformat()
    }
    
    logger.info(f"报告发送完成: {result}")
    return result


async def health_check_task(**kwargs) -> Dict[str, Any]:
    """
    健康检查任务示例
    """
    services = kwargs.get('services', ['database', 'redis', 'api'])
    
    logger.info("开始执行健康检查")
    
    results = {}
    
    for service in services:
        # 模拟健康检查
        await asyncio.sleep(0.5)
        
        # 随机生成检查结果
        is_healthy = random.choice([True, True, True, False])  # 75%概率健康
        response_time = random.randint(10, 500)  # 10-500ms
        
        results[service] = {
            'healthy': is_healthy,
            'response_time_ms': response_time,
            'checked_at': datetime.now().isoformat()
        }
    
    overall_health = all(result['healthy'] for result in results.values())
    
    result = {
        'overall_health': overall_health,
        'services': results,
        'completed_at': datetime.now().isoformat()
    }
    
    logger.info(f"健康检查完成: 整体状态={'健康' if overall_health else '异常'}")
    return result


async def log_analysis_task(**kwargs) -> Dict[str, Any]:
    """
    日志分析任务示例
    """
    log_path = kwargs.get('log_path', '/var/log/app.log')
    time_range = kwargs.get('time_range', '1h')
    
    logger.info(f"开始分析日志文件: {log_path} (时间范围: {time_range})")
    
    # 模拟日志分析过程
    await asyncio.sleep(4)
    
    # 模拟分析结果
    total_lines = random.randint(10000, 100000)
    error_count = random.randint(0, 100)
    warning_count = random.randint(0, 500)
    
    result = {
        'log_path': log_path,
        'time_range': time_range,
        'total_lines': total_lines,
        'error_count': error_count,
        'warning_count': warning_count,
        'error_rate': error_count / total_lines if total_lines > 0 else 0,
        'completed_at': datetime.now().isoformat()
    }
    
    logger.info(f"日志分析完成: {result}")
    return result


def sync_hello_task(**kwargs) -> str:
    """
    同步任务示例（非异步）
    """
    name = kwargs.get('name', 'Sync World')
    message = f"Hello from sync task, {name}! Time: {datetime.now()}"
    logger.info(f"执行同步Hello任务: {message}")
    return message


async def error_task(**kwargs) -> None:
    """
    故意出错的任务，用于测试错误处理
    """
    error_type = kwargs.get('error_type', 'generic')
    
    logger.info(f"执行错误任务，错误类型: {error_type}")
    
    if error_type == 'timeout':
        await asyncio.sleep(10)  # 模拟超时
    elif error_type == 'division':
        result = 1 / 0  # 除零错误
    elif error_type == 'value':
        raise ValueError("这是一个值错误示例")
    elif error_type == 'runtime':
        raise RuntimeError("这是一个运行时错误示例")
    else:
        raise Exception("这是一个通用错误示例")


async def long_running_task(**kwargs) -> Dict[str, Any]:
    """
    长时间运行的任务示例
    """
    duration = kwargs.get('duration', 30)  # 默认30秒
    step_interval = kwargs.get('step_interval', 5)  # 每5秒记录一次进度
    
    logger.info(f"开始执行长时间任务，预计运行 {duration} 秒")
    
    start_time = datetime.now()
    steps = duration // step_interval
    
    for i in range(steps):
        await asyncio.sleep(step_interval)
        progress = ((i + 1) / steps) * 100
        logger.info(f"长时间任务进度: {progress:.1f}%")
    
    end_time = datetime.now()
    actual_duration = (end_time - start_time).total_seconds()
    
    result = {
        'expected_duration': duration,
        'actual_duration': actual_duration,
        'steps_completed': steps,
        'started_at': start_time.isoformat(),
        'completed_at': end_time.isoformat()
    }
    
    logger.info(f"长时间任务完成: {result}")
    return result
