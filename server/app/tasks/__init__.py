"""
动态任务管理模块

提供任务的创建、调度、执行和监控功能
"""

from .manager import TaskManager
from .executor import TaskExecutor
from .scheduler import TaskScheduler
from .types import TaskFunction, TaskResult

# 全局任务管理器实例
task_manager: TaskManager = None


def get_task_manager() -> TaskManager:
    """获取全局任务管理器实例"""
    global task_manager
    if task_manager is None:
        task_manager = TaskManager()
    return task_manager


async def init_task_system():
    """初始化任务系统"""
    manager = get_task_manager()
    await manager.initialize()


async def shutdown_task_system():
    """关闭任务系统"""
    manager = get_task_manager()
    await manager.shutdown()


__all__ = [
    "TaskManager",
    "TaskExecutor", 
    "TaskScheduler",
    "TaskFunction",
    "TaskResult",
    "get_task_manager",
    "init_task_system",
    "shutdown_task_system"
]
