"""
任务模板配置

提供预定义的任务模板，方便用户快速创建常用任务
"""

from typing import Dict, List, Any

# 任务模板定义
TASK_TEMPLATES: List[Dict[str, Any]] = [
    {
        "name": "Hello World 任务",
        "description": "简单的问候任务，用于测试任务系统",
        "module_path": "app.tasks.examples",
        "function_name": "hello_world_task",
        "parameters": {
            "name": "World"
        },
        "schedule_examples": {
            "cron": {
                "expression": "0 9 * * *",  # 每天上午9点
                "description": "每天上午9点执行"
            },
            "interval": {
                "minutes": 30,
                "description": "每30分钟执行一次"
            }
        },
        "category": "测试"
    },
    {
        "name": "数据清理任务",
        "description": "清理指定天数前的历史数据",
        "module_path": "app.tasks.examples",
        "function_name": "data_cleanup_task",
        "parameters": {
            "days": 30,
            "table": "logs"
        },
        "schedule_examples": {
            "cron": {
                "expression": "0 2 * * 0",  # 每周日凌晨2点
                "description": "每周日凌晨2点执行"
            },
            "interval": {
                "days": 7,
                "description": "每7天执行一次"
            }
        },
        "category": "维护"
    },
    {
        "name": "数据库备份任务",
        "description": "定期备份数据库",
        "module_path": "app.tasks.examples",
        "function_name": "backup_database_task",
        "parameters": {
            "database": "zcws",
            "backup_path": "/data/backups"
        },
        "schedule_examples": {
            "cron": {
                "expression": "0 1 * * *",  # 每天凌晨1点
                "description": "每天凌晨1点执行"
            },
            "interval": {
                "hours": 12,
                "description": "每12小时执行一次"
            }
        },
        "category": "备份"
    },
    {
        "name": "发送报告任务",
        "description": "生成并发送定期报告",
        "module_path": "app.tasks.examples",
        "function_name": "send_report_task",
        "parameters": {
            "report_type": "daily",
            "recipients": ["<EMAIL>"]
        },
        "schedule_examples": {
            "cron": {
                "expression": "0 8 * * 1-5",  # 工作日上午8点
                "description": "工作日上午8点执行"
            },
            "interval": {
                "days": 1,
                "description": "每天执行一次"
            }
        },
        "category": "报告"
    },
    {
        "name": "健康检查任务",
        "description": "检查系统各组件的健康状态",
        "module_path": "app.tasks.examples",
        "function_name": "health_check_task",
        "parameters": {
            "services": ["database", "redis", "api"]
        },
        "schedule_examples": {
            "interval": {
                "minutes": 5,
                "description": "每5分钟执行一次"
            },
            "cron": {
                "expression": "*/5 * * * *",  # 每5分钟
                "description": "每5分钟执行一次"
            }
        },
        "category": "监控"
    },
    {
        "name": "日志分析任务",
        "description": "分析应用日志，统计错误和警告",
        "module_path": "app.tasks.examples",
        "function_name": "log_analysis_task",
        "parameters": {
            "log_path": "/var/log/app.log",
            "time_range": "1h"
        },
        "schedule_examples": {
            "interval": {
                "hours": 1,
                "description": "每小时执行一次"
            },
            "cron": {
                "expression": "0 * * * *",  # 每小时整点
                "description": "每小时整点执行"
            }
        },
        "category": "分析"
    },
    {
        "name": "同步任务示例",
        "description": "同步（非异步）任务示例",
        "module_path": "app.tasks.examples",
        "function_name": "sync_hello_task",
        "parameters": {
            "name": "Sync World"
        },
        "schedule_examples": {
            "interval": {
                "minutes": 10,
                "description": "每10分钟执行一次"
            }
        },
        "category": "测试"
    },
    {
        "name": "长时间运行任务",
        "description": "模拟长时间运行的任务",
        "module_path": "app.tasks.examples",
        "function_name": "long_running_task",
        "parameters": {
            "duration": 60,
            "step_interval": 10
        },
        "schedule_examples": {
            "cron": {
                "expression": "0 0 * * *",  # 每天午夜
                "description": "每天午夜执行"
            }
        },
        "category": "测试"
    }
]

# 常用的Cron表达式示例
CRON_EXAMPLES = [
    {
        "expression": "0 0 * * *",
        "description": "每天午夜执行"
    },
    {
        "expression": "0 9 * * *",
        "description": "每天上午9点执行"
    },
    {
        "expression": "0 9 * * 1-5",
        "description": "工作日上午9点执行"
    },
    {
        "expression": "0 0 * * 0",
        "description": "每周日午夜执行"
    },
    {
        "expression": "0 0 1 * *",
        "description": "每月1号午夜执行"
    },
    {
        "expression": "*/5 * * * *",
        "description": "每5分钟执行"
    },
    {
        "expression": "0 */2 * * *",
        "description": "每2小时执行"
    },
    {
        "expression": "0 8,12,18 * * *",
        "description": "每天8点、12点、18点执行"
    },
    {
        "expression": "0 9-17 * * 1-5",
        "description": "工作日9-17点每小时执行"
    }
]

# 间隔任务示例
INTERVAL_EXAMPLES = [
    {
        "config": {"seconds": 30},
        "description": "每30秒执行"
    },
    {
        "config": {"minutes": 5},
        "description": "每5分钟执行"
    },
    {
        "config": {"minutes": 30},
        "description": "每30分钟执行"
    },
    {
        "config": {"hours": 1},
        "description": "每小时执行"
    },
    {
        "config": {"hours": 6},
        "description": "每6小时执行"
    },
    {
        "config": {"hours": 12},
        "description": "每12小时执行"
    },
    {
        "config": {"days": 1},
        "description": "每天执行"
    },
    {
        "config": {"days": 7},
        "description": "每周执行"
    }
]

# 任务分类
TASK_CATEGORIES = [
    "测试",
    "维护", 
    "备份",
    "报告",
    "监控",
    "分析",
    "清理",
    "同步",
    "通知",
    "其他"
]


def get_task_templates() -> List[Dict[str, Any]]:
    """获取所有任务模板"""
    return TASK_TEMPLATES


def get_task_template_by_name(name: str) -> Dict[str, Any]:
    """根据名称获取任务模板"""
    for template in TASK_TEMPLATES:
        if template["name"] == name:
            return template
    return None


def get_templates_by_category(category: str) -> List[Dict[str, Any]]:
    """根据分类获取任务模板"""
    return [template for template in TASK_TEMPLATES if template.get("category") == category]


def get_cron_examples() -> List[Dict[str, str]]:
    """获取Cron表达式示例"""
    return CRON_EXAMPLES


def get_interval_examples() -> List[Dict[str, Any]]:
    """获取间隔任务示例"""
    return INTERVAL_EXAMPLES


def get_task_categories() -> List[str]:
    """获取任务分类列表"""
    return TASK_CATEGORIES
