from typing import Any, Generic, Optional, Self, TypeVar, Union, get_origin

from pydantic import constr, BaseModel, Field, field_validator, model_validator
from tortoise import Model
from tortoise.functions import Count
from tortoise.queryset import QuerySet

from app.api.resp import ResponseBase, MessageType, assign_response

T = TypeVar("T")
UsernameStr = constr(pattern=r"^[a-zA-Z\d\-_]+$", max_length=20, min_length=1)
PasswordStr = constr(min_length=8)
CodeStr = constr(to_lower=True, max_length=4, min_length=4)
NickNameStr = constr(max_length=20, min_length=1)


class CodeModel(BaseModel):
    code: CodeStr = Field(description="图形验证码")


class UserAvatarModel(BaseModel):
    avatar: Optional[str] = Field(default=None, description="头像URL")

    @field_validator("avatar", mode="before")
    def check_avatar(cls, v):
        if v:
            return f"/uploads/avatar/{v}"
        return v


class BaseResp(BaseModel, Generic[T]):
    success: bool = Field(default=True, description="状态码")
    code: int = Field(default=0, description="状态码")
    msg: str = Field(default="ok", description="信息")
    desc: str | None = Field(default=None, description="描述信息")
    data: T | None = None

    @classmethod
    def get_data_types(cls: BaseModel) -> tuple[type]:
        if get_origin(cls.model_fields["data"].annotation) is Union:
            return cls.model_fields["data"].annotation.__args__
        return (cls.model_fields["data"].annotation,)

    @model_validator(mode="before")
    def validate_base_before(cls, value: Any) -> Self:
        if type(value) in cls.get_data_types():
            return {"data": value}
        if type(value) is type:
            if issubclass(value, ResponseBase):
                return value()
        elif isinstance(value, ResponseBase):
            return value
        if value is None:
            return {}
        if isinstance(value, str):
            return {"msg": value}
        if isinstance(value, tuple):
            assert len(value) == 2 and type(value[0]) is int, "Tuple must be of length 2 with first element as int"
            code, msg = value
            return {"code": code, "msg": msg}
        return value


class PageDataModel(BaseModel, Generic[T]):
    items: list[T] = Field(default_factory=list, description="数据列表")
    total: int = Field(default=0, description="总记录数")
    page_no: int = Field(default=1, description="当前页码")
    page_total: int | None = None

    @model_validator(mode="after")
    def set_page_total(self) -> Self:
        if self.total > 0:
            self.page_total = (self.total + len(self.items) - 1) // len(self.items)
        else:
            self.page_total = 0
        return self


class BasePageResp(BaseResp, Generic[T]):
    data: PageDataModel[T] = Field(default_factory=PageDataModel, description="分页数据")

    @model_validator(mode="before")
    def validate_page_resp(cls, value: Any) -> Self:
        if isinstance(value, dict) and "items" in value and "page_no" in value and "total" in value:
            return {"data": value}
        return value


class UploadResp(BaseResp):
    data: str = Field(description="文件名")


@assign_response(True, MessageType.INFO, "无数据")
class PageNoDataResp(ResponseBase): ...


def get_page_no(total, page_size, page_no):
    if total == 0:
        raise PageNoDataResp({"page_no": 1, "total": 0, "items": []})
    if total < (page_no - 1) * page_size:
        return total // page_size + int(total % page_size != 0)
    return page_no


MODEL = TypeVar("MODEL", bound=Model)


async def get_page_result(
    page_size: int,
    page_no: int,
    queryset: QuerySet["MODEL"],
    prefetch_related: list[str] | None = None,
    distinct=False,
) -> tuple[list["MODEL"], int, int]:
    if distinct:
        total_qs = queryset.annotate(count=Count("id")).group_by("id").values_list("count")
        total = len(await total_qs)
    else:
        total = await queryset.count()

    page_no = get_page_no(total, page_size, page_no)
    qs = queryset.offset((page_no - 1) * page_size).limit(page_size)
    if prefetch_related:
        qs = qs.prefetch_related(*prefetch_related)
    if distinct:
        qs = qs.distinct()
    result = await qs
    return result, page_no, total


class DictToObject:
    def __init__(self, dictionary):
        for key, value in dictionary.items():
            if isinstance(value, dict):
                setattr(self, key, DictToObject(value))
            else:
                setattr(self, key, value)
