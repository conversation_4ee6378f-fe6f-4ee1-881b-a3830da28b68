from textwrap import dedent

from crawl4ai import BrowserConfig, Crawl4aiDockerClient, CrawlerRunConfig, LLMConfig, LLMExtractionStrategy
from fastapi import APIRouter
from pydantic import BaseModel, Field

from app.api.base import assign_response, ResponseBase, BaseResp

router = APIRouter()


@assign_response(True)
class TestResponse(ResponseBase): ...


class NewsArticle(BaseModel):
    """新闻文章数据模型"""

    title: str = Field(..., description="文章标题")
    content: str = Field(..., description="文章内容摘要")
    time: str = Field(..., description="发布时间")
    url: str = Field(..., description="文章链接")
    category: str | None = Field(None, description="分类标签")


class ExtractionRules(BaseModel):
    """提取规则数据模型"""

    article_list_selector: str = Field(..., description="文章列表容器选择器")
    article_item_selector: str = Field(..., description="单个文章项选择器")
    title_selector: str = Field(..., description="标题选择器")
    content_selector: str = Field(..., description="内容选择器")
    time_selector: str = Field(..., description="时间选择器")
    link_selector: str = Field(..., description="链接选择器")


class NewsResponse(BaseModel):
    """AI提取响应模型"""

    articles: list[NewsArticle] = Field(..., description="提取的文章列表")
    extraction_rules: ExtractionRules = Field(..., description="分析得出的CSS选择器规则")


@router.get("", name="API测试", response_model=BaseResp)
async def test_api():
    async with Crawl4aiDockerClient(base_url="http://crawl:11235", timeout=120, verbose=True) as client:
        result = await client.crawl(
            urls=["https://www.jinse.cn/lives"],
            browser_config=BrowserConfig(headless=True, verbose=True),
            crawler_config=CrawlerRunConfig(
                excluded_tags=["form", "header", "footer", "nav", "script", "style", "noscript", "link"],
                extraction_strategy=LLMExtractionStrategy(
                    llm_config=LLMConfig(
                        provider="deepseek/deepseek-chat",
                        api_token="sk-26496ce1acb749d89e687774675e8097",
                    ),
                    chunk_token_threshold=4000,
                    apply_chunking=True,
                    force_json_response=True,
                    input_format="html",  # 使用HTML格式让AI更好地分析结构
                    extra_args={"temperature": 0.1},
                    schema=NewsResponse.model_json_schema(),
                    extraction_type="schema",
                    instruction=dedent(
                        f"""\
                        你是一个专业的网页结构分析专家。请从网站分析并提取以下内容：
                        
                        1. 提取最新的新闻/文章信息
                        2. **重要**：同时分析网页的HTML结构，生成准确的CSS选择器规则
                        
                        对于每篇文章，提取：
                        - title: 文章标题
                        - content: 文章内容摘要（200字以内）
                        - time: 发布时间（保持原格式）
                        - url: 文章链接（完整URL）
                        - category: 分类标签（如果有）
                        
                        对于提取规则，分析并生成：
                        - article_list_selector: 文章列表容器的CSS选择器
                        - article_item_selector: 单个文章项的CSS选择器
                        - title_selector: 标题元素的CSS选择器
                        - content_selector: 内容元素的CSS选择器
                        - time_selector: 时间元素的CSS选择器
                        - link_selector: 链接元素的CSS选择器
                        
                        请确保生成的CSS选择器准确可用，能够在后续的非AI爬取中正确提取数据。
                        """
                    ),
                ),
            ),
        )
        if result.success and result.extracted_content:
            return TestResponse(result.extracted_content)
    return {"message": "API测试成功"}
