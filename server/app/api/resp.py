from enum import IntEnum
from typing import Any, Callable

# 响应编码的位数，用于生成唯一的响应代码
RESPONSE_SIZE = 10


class MessageType(IntEnum):
    """
    消息类型枚举，用于控制前端的Message提示显示
    使用位移操作确保每个类型都有唯一的数值范围，支持与其他类型组合使用
    """

    _size = RESPONSE_SIZE

    SILENT = 0 << _size  # 静默消息（不显示Message提示）
    ERROR = 1 << _size  # 错误消息（显示红色Message）
    WARNING = 2 << _size  # 警告消息（显示黄色Message）
    SUCCESS = 3 << _size  # 成功消息（显示绿色Message）
    INFO = 4 << _size  # 信息消息（显示蓝色Message）
    UNKNOWN = 5 << _size  # 未知类型消息


class NotificationType(IntEnum):
    """
    通知类型枚举，用于控制前端的Notification通知显示
    与MessageType分别控制不同的UI组件，可以通过位运算组合使用
    """

    _size = RESPONSE_SIZE + 3

    SILENT = 0 << _size  # 静默通知（不显示Notification）
    ERROR = 1 << _size  # 错误通知（显示错误样式的Notification）
    WARNING = 2 << _size  # 警告通知（显示警告样式的Notification）
    SUCCESS = 3 << _size  # 成功通知（显示成功样式的Notification）
    INFO = 4 << _size  # 信息通知（显示信息样式的Notification）
    UNKNOWN = 5 << _size  # 未知类型通知


class ResponseType(IntEnum):
    """
    响应类型枚举，用于标识特定的HTTP响应类型
    主要用于系统级别的响应处理
    """

    _size = RESPONSE_SIZE + 6

    MANUAL = 0 << _size  # 手动处理响应
    UNAUTHORIZED = 1 << _size  # ���授权响应 (401)
    INTERNAL_SERVER_ERROR = 2 << _size  # 服务器内部错误响应 (500)


class ResponseBase(Exception):
    """
    响应基类，继承自Exception，既可以作为正常返回值，也可以作为异常抛出

    设计特点：
    1. 继承Exception，支持作为异常抛出
    2. 统一的响应格式，包含success、code、msg、desc、data字段
    3. 可以传入数据和自定义描述信息
    """

    success: bool = True  # 响应是否成功
    code: int = 0  # 响应代码，由assign_response装饰器自动分配
    msg: str = "ok"  # 响应消息
    desc: str | None = None  # 响应描述，可选的详细信息
    data: Any = None  # 响应数据

    def __init__(self, data: Any = None, desc: str | None = None):
        """
        初始化响应对象

        Args:
            data: 响应数据，可以是任意类型
            desc: 响应描述信息，会覆盖类级别的desc
        """
        self.data = data
        if desc is not None:
            self.desc = desc

    def model_dump(self) -> dict:
        """
        将响应对象转换为字典格式，用于JSON序列化

        Returns:
            包含所有响应字段的字典
        """
        return {
            "success": self.success,
            "code": self.code,
            "msg": self.msg,
            "desc": self.desc,
            "data": self.data,
        }


# 全局响应注册，存储所有定义的响应类
# key: 模块名.类名，value: 响应类
ResponseList: dict[str, type[ResponseBase]] = {}


def get_all_response() -> list[dict]:
    """
    获取所有已注册的响应类信息

    用于API文档生成或调试，返回所有响应类的基本信息

    Returns:
        包含所有响应类信息的列表，每个元素是一个字典，包含：
        - code: 响应代码
        - success: 是否成功
        - msg: 响应消息
        - position: 响应类的位置（模块名.类名）
    """
    return list(
        map(
            lambda cls: {
                cls.code: {
                    "success": cls.success,
                    "msg": cls.msg,
                    "position": f"{cls.__module__}.{cls.__name__}",
                },
            },
            ResponseList.values(),
        )
    )


def assign_response(success: bool, code: int = 0, msg: str = "ok", desc: str | None = None) -> Callable:
    """
    响应类装饰器，用于为响应类分配唯一的代码和消息

    这是一个装饰器工厂函数，返回一个装饰器来配置响应类的属性。
    支持通过位运算符组合不同类型的代码，同时控制前端的多种UI显示效果。

    Args:
        success: 响应是否表示成功状态
        code: 基础代码，支持以下几种使用方式：
            - 单一类型: MessageType.ERROR, NotificationType.SUCCESS, ResponseType.UNAUTHORIZED
            - 组合类型: MessageType.ERROR | NotificationType.ERROR（同时显示Message和Notification）
            - 复杂组合: MessageType.ERROR | ResponseType.UNAUTHORIZED（错误消息 + 401状态）
        msg: 响应消息文本
        desc: 可选的详细描述信息

    Returns:
        装饰器函数，用于装饰ResponseBase的子类

    Example:
        # 基础用法 - 只显示Message
        @assign_response(True, MessageType.SUCCESS, "用户创建成功")
        class CreateUserSuccess(ResponseBase): ...

        # 组合用法 - 同时显示Message和Notification
        @assign_response(False, MessageType.ERROR | NotificationType.ERROR, "登录失败")
        class LoginFailed(ResponseBase): ...

        # 复杂组合 - 错误消息 + 未授权状态
        @assign_response(False, MessageType.ERROR | ResponseType.UNAUTHORIZED, "访问被拒绝")
        class AccessDenied(ResponseBase): ...

        # 三重组合 - Message + Notification + 特定HTTP状态
        @assign_response(False, MessageType.ERROR | NotificationType.ERROR | ResponseType.UNAUTHORIZED, "身份验证失败")
        class AuthenticationFailed(ResponseBase): ...
    """

    def f(cls: type[ResponseBase]) -> type[ResponseBase]:
        """
        实际的装饰器函数

        Args:
            cls: 要装饰的响应类

        Returns:
            配置后的响应类
        """
        global ResponseList
        # 生成唯一的键，格式为"模块名.类名"
        key = f"{cls.__module__}.{cls.__name__}"

        # 如果已经注册过，直接返回（避免重复注册）
        if key in ResponseList:
            return cls

        # 生成唯一的响应ID，从1开始递增
        response_id = len(ResponseList) + 1

        # 检查响应ID是否超过限制（2^RESPONSE_SIZE）
        assert response_id < (1 << RESPONSE_SIZE), "Response ID exceeds limit"

        # 注册响应类到全局列表
        ResponseList[key] = cls

        # 设置响应类的属性
        cls.success = success
        cls.code = response_id + code  # 最终代码 = 响应ID + 基础代码
        cls.msg = msg
        cls.desc = desc

        return cls

    return f
