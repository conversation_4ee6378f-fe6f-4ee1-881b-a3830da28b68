"""
网页文章爬取API接口

提供RESTful API接口来使用通用文章爬取功能
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks, Query
from pydantic import BaseModel, Field, field_validator, validator

from app.api.base import assign_response, ResponseBase, BaseResp
from app.crawler import (
    crawl_articles,
    crawl_articles_with_pagination,
    batch_crawl_articles,
    CrawlConfig,
    CrawlResult,
    ArticleData,
    ExtractionStrategy,
    CrawlMethod,
    get_template,
    list_templates,
)

router = APIRouter(tags=["文章爬取"])


# 请求模型
class CrawlRequest(BaseModel):
    """爬取请求模型"""

    url: str = Field(..., description="目标URL")
    extraction_strategy: ExtractionStrategy = Field(ExtractionStrategy.CUSTOM_SCRIPT, description="提取策略")
    custom_script: Optional[str] = Field(None, description="自定义Python脚本")
    css_selectors: Optional[Dict[str, str]] = Field(None, description="CSS选择器映射")

    # HTTP配置
    method: CrawlMethod = Field(CrawlMethod.GET, description="HTTP方法")
    headers: Optional[Dict[str, str]] = Field(None, description="请求头")
    params: Optional[Dict[str, Any]] = Field(None, description="请求参数")

    # 分页配置
    enable_pagination: bool = Field(False, description="是否启用分页")
    max_pages: int = Field(5, description="最大页数")

    # 反爬虫配置
    request_delay: float = Field(1.0, description="请求间隔(秒)")
    user_agent: Optional[str] = Field(None, description="User-Agent")
    timeout: int = Field(30, description="超时时间(秒)")

    # 浏览器配置
    use_browser: bool = Field(False, description="是否使用浏览器渲染")

    @field_validator("max_pages")
    def validate_max_pages(cls, v):
        if v > 20:
            raise ValueError("最大页数不能超过20")
        return v

    @field_validator("request_delay")
    def validate_request_delay(cls, v):
        if v < 0.5:
            raise ValueError("请求间隔不能小于0.5秒")
        return v


class BatchCrawlRequest(BaseModel):
    """批量爬取请求模型"""

    crawl_configs: List[CrawlRequest] = Field(..., description="爬取配置列表")

    @field_validator("crawl_configs")
    def validate_crawl_configs(cls, v):
        if len(v) > 10:
            raise ValueError("批量爬取最多支持10个URL")
        return v


# 响应模型
@assign_response(True, msg="文章爬取成功")
class CrawlResponse(ResponseBase):
    pass


@assign_response(True, msg="批量爬取成功")
class BatchCrawlResponse(ResponseBase):
    pass


@assign_response(True, msg="获取模板成功")
class TemplateResponse(ResponseBase):
    pass


# API接口
@router.post("/crawl", response_model=BaseResp[CrawlResult], name="爬取文章")
async def crawl_articles_api(request: CrawlRequest):
    """
    爬取单个网站的文章

    支持多种提取策略：
    - custom_script: 自定义Python脚本
    - css_selector: CSS选择器
    - regex: 正则表达式
    """
    try:
        # 构建爬取配置
        config = CrawlConfig(
            url=request.url,
            method=request.method,
            extraction_strategy=request.extraction_strategy,
            custom_script=request.custom_script,
            css_selectors=request.css_selectors or {},
            headers=request.headers or {},
            params=request.params or {},
            enable_pagination=request.enable_pagination,
            max_pages=request.max_pages,
            request_delay=request.request_delay,
            user_agent=request.user_agent,
            timeout=request.timeout,
            use_browser=request.use_browser,
        )

        # 执行爬取
        if request.enable_pagination:
            result = await crawl_articles_with_pagination(config)
        else:
            result = await crawl_articles(config)

        if result.success:
            return CrawlResponse(data=result)
        else:
            raise HTTPException(status_code=400, detail=result.error_message)

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"爬取过程中发生错误: {str(e)}")


@router.post("/batch-crawl", response_model=BaseResp[List[CrawlResult]], name="批量爬取文章")
async def batch_crawl_articles_api(request: BatchCrawlRequest):
    """
    批量爬取多个网站的文章

    最多支持同时爬取10个网站
    """
    try:
        # 构建爬取配置列表
        configs = []
        for crawl_req in request.crawl_configs:
            config = CrawlConfig(
                url=crawl_req.url,
                method=crawl_req.method,
                extraction_strategy=crawl_req.extraction_strategy,
                custom_script=crawl_req.custom_script,
                css_selectors=crawl_req.css_selectors or {},
                headers=crawl_req.headers or {},
                params=crawl_req.params or {},
                enable_pagination=crawl_req.enable_pagination,
                max_pages=crawl_req.max_pages,
                request_delay=crawl_req.request_delay,
                user_agent=crawl_req.user_agent,
                timeout=crawl_req.timeout,
                use_browser=crawl_req.use_browser,
            )
            configs.append(config)

        # 执行批量爬取
        results = await batch_crawl_articles(configs)

        return BatchCrawlResponse(data=results)

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量爬取过程中发生错误: {str(e)}")


@router.get("/templates", response_model=BaseResp[Dict[str, Any]], name="获取脚本模板")
async def get_script_templates():
    """
    获取所有可用的脚本模板

    返回模板名称、描述和脚本内容
    """
    try:
        templates = list_templates()

        # 添加完整的脚本内容
        full_templates = {}
        for name, info in templates.items():
            full_templates[name] = {
                "name": info["name"],
                "description": info["description"],
                "script": get_template(name),
            }

        return TemplateResponse(data=full_templates)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模板失败: {str(e)}")


@router.get("/templates/{template_name}", response_model=BaseResp[Dict[str, str]], name="获取指定模板")
async def get_script_template(template_name: str):
    """
    获取指定名称的脚本模板

    可用模板：
    - basic: 基础模板
    - news: 新闻网站模板
    - blog: 博客模板
    - api: API接口模板
    """
    try:
        script = get_template(template_name)
        templates = list_templates()

        if template_name not in templates:
            raise HTTPException(status_code=404, detail=f"模板 '{template_name}' 不存在")

        template_info = templates[template_name]

        return TemplateResponse(
            data={"name": template_info["name"], "description": template_info["description"], "script": script}
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模板失败: {str(e)}")


@router.post("/test-script", response_model=BaseResp[dict[str, Any]], name="测试脚本")
async def test_script(
    url: str = Query(..., description="测试URL"), script: str = Query(..., description="要测试的脚本")
):
    """
    测试自定义脚本是否能正确提取数据

    返回提取结果的预览，不会保存数据
    """
    try:
        # 构建测试配置
        config = CrawlConfig(
            url=url,
            extraction_strategy=ExtractionStrategy.CUSTOM_SCRIPT,
            custom_script=script,
            request_delay=1.0,
            timeout=15,  # 测试时使用较短的超时时间
        )

        # 执行爬取（仅爬取一页）
        result = await crawl_articles(config)

        if result.success:
            # 返回预览数据
            preview_articles = result.articles[:5]  # 最多返回5篇文章

            return TemplateResponse(
                data={
                    "success": True,
                    "total_articles": len(result.articles),
                    "preview_articles": [
                        {
                            "title": article.title,
                            "url": article.url,
                            "author": article.author,
                            "publish_time": article.publish_time,
                            "content_preview": (
                                article.content[:100] + "..."
                                if article.content and len(article.content) > 100
                                else article.content
                            ),
                        }
                        for article in preview_articles
                    ],
                    "execution_time": result.execution_time,
                    "metadata": result.metadata,
                }
            )
        else:
            return TemplateResponse(
                data={"success": False, "error_message": result.error_message, "execution_time": result.execution_time}
            )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"脚本测试失败: {str(e)}")


@router.get("/strategies", response_model=BaseResp[List[Dict[str, str]]], name="获取提取策略")
async def get_extraction_strategies():
    """
    获取所有可用的数据提取策略
    """
    strategies = [
        {
            "value": ExtractionStrategy.CUSTOM_SCRIPT.value,
            "name": "自定义脚本",
            "description": "使用Python脚本自定义数据提取逻辑，最灵活的方式",
        },
        {
            "value": ExtractionStrategy.CSS_SELECTOR.value,
            "name": "CSS选择器",
            "description": "使用CSS选择器提取数据，适合结构化页面",
        },
        {"value": ExtractionStrategy.REGEX.value, "name": "正则表达式", "description": "使用正则表达式匹配和提取数据"},
        {
            "value": ExtractionStrategy.LLM.value,
            "name": "LLM提取",
            "description": "使用大语言模型智能提取数据（实验性功能）",
        },
    ]

    return TemplateResponse(data=strategies)
