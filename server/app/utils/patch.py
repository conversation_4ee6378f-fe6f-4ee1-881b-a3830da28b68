from dowhen import when


def patch_QueryParams():
    from urllib.parse import parse_qsl

    when(parse_qsl, "if not separator").do("keep_blank_values = False")


def patch_ServerErrorMiddleware():
    from starlette.middleware.errors import ServerErrorMiddleware

    when(ServerErrorMiddleware, "if self.debug").do("request = Request(scope, receive)")


def patch_Crawl4aiDockerClient():
    from crawl4ai import Crawl4aiDockerClient

    when(Crawl4aiDockerClient, "is_streaming = crawler_config and crawler_config.stream").do("print(data)")


def patch_all():
    patch_QueryParams()
    patch_ServerErrorMiddleware()
    patch_Crawl4aiDockerClient()
