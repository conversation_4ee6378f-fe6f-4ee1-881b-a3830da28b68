from asyncio import sleep
from functools import partial
from pickle import HIGHEST_PROTOCOL, dumps, loads

from redis.asyncio import Redis
from redis.exceptions import LockNotOwnedError

from app.static import CACHE_REDIS_URL, MANAGEMENT_SESSION_REDIS_URL

cacheRedis: Redis = Redis.from_url(CACHE_REDIS_URL)

sessionRedis: Redis = Redis.from_url(MANAGEMENT_SESSION_REDIS_URL)

cache_dumps = partial(dumps, protocol=HIGHEST_PROTOCOL)
cache_loads = partial(loads)


def cached_before(cache_key_perfix: str, timeout=3600):
    """
    缓存装饰器，用于缓存函数执行结果

    Args:
        cache_key_perfix (str): 缓存键前缀
        timeout (int): 缓存过期时间，默认3600秒(1小时)

    Returns:
        装饰器函数

    Example:
        @cached_before("user_info", timeout=1800)
        async def get_user_info(user_id: int, include_profile: bool = True):
            # 模拟从数据库获取用户信息
            user_data = await db.fetch_user(user_id)
            if include_profile:
                user_data.update(await db.fetch_user_profile(user_id))
            return user_data

        # 使用示例：
        # 第一次调用，会执行函数并缓存结果
        user = await get_user_info(123, include_profile=True)

        # 第二次调用相同参数，直接从缓存返回
        user = await get_user_info(123, include_profile=True)

        # 删除缓存
        await get_user_info(123, include_profile=True, delete=True)
    """

    def make_key(*args, **kwargs):
        """
        根据函数参数生成缓存键和锁键

        Args:
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            tuple: (缓存键, 锁键)
        """
        _cache_key = [cache_key_perfix]
        # 将所有位置参数转为字符串并添加到缓存键中
        for x in args:
            _cache_key.append(str(x))
        # 将所有关键字参数的值转为字符串并添加到缓存键中
        for k, v in kwargs.items():
            _cache_key.append(str(v))
        _cache_key = "-".join(_cache_key)
        return _cache_key, _cache_key + "-lock"

    def decorator(f: callable):
        """
        装饰器函数

        Args:
            f (callable): 被装饰的函数

        Returns:
            装饰后的异步函数
        """

        async def decorated_function(*args, delete=False, **kwargs):
            """
            装饰后的函数，实现缓存逻辑

            Args:
                *args: 原函数的位置参数
                delete (bool): 是否删除缓存，默认False
                **kwargs: 原函数的关键字参数

            Returns:
                函数执行结果或缓存中的结果
            """
            cache_key, lock_key = make_key(*args, **kwargs)

            # 如果delete为True，删除缓存并返回None
            if delete:
                await cacheRedis.delete(cache_key)
                return None

            # 创建分布式锁，防止缓存击穿
            lock = cacheRedis.lock(lock_key, timeout=10)

            while True:
                # 尝试从缓存中获取结果
                rv = await cacheRedis.get(cache_key)
                if rv is not None:
                    # 缓存命中，反序列化并返回结果
                    return cache_loads(rv)

                # 缓存未命中，尝试获取锁
                if not await lock.acquire(blocking=False):
                    # 获取锁失败，短暂等待后重试
                    await sleep(0.1)
                    continue

                # 成功获取锁，执行原函数并缓存结果
                try:
                    rv = await f(*args, **kwargs)
                    # 将结果序列化后存入缓存
                    await cacheRedis.set(cache_key, cache_dumps(rv), ex=timeout)
                finally:
                    # 释放锁
                    try:
                        await lock.release()
                    except LockNotOwnedError:
                        # 锁已被其他进程释放，忽略异常
                        pass
                return rv

        return decorated_function

    return decorator
