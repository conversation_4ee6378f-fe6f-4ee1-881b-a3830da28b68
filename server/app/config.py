from pydantic import BaseModel

from app.models import Config
from app.cache import cached_before


async def update_config(cfg: dict):
    """
    从字典更新配置到数据库
    :param cfg:
    :return:
    """
    for key in cfg:
        await Config.update_or_create(key=key, defaults={"value": cfg.get(key, "")})


class MailConfigModel(BaseModel):
    MAIL_SENDER_NAME: str = ""
    MAIL_SERVER: str = ""
    MAIL_PORT: int = 25
    MAIL_USE_AUTH: bool = True
    MAIL_USERNAME: str = ""
    MAIL_PASSWORD: str = ""
    MAIL_TLS: bool = True
    MAIL_SSL: bool = False
    MAIL_SENDER_ADDR: str = ""
    MAIL_CODE_TEMPLATE: str = "验证码: <b>{code}</b>"


class SystemConfigModel(BaseModel):
    ENABLE_REGISTER: bool = False
    ENABLE_LOGIN: bool = True
    ENABLE_EMAIL_VERIFY: bool = True
    ADMIN_EXPIRED_TIME: int = 60
    USER_EXPIRED_TIME: int = 60 * 24
    SERVER_IP: str = "***********"
    SYSTEM_LOGO: str = "logo.png"
    SYSTEM_NAME: str = "恒踪"


# 获取邮件相关配置
async def Mail() -> MailConfigModel:
    data = await get_all_from_db()
    return MailConfigModel(**data)


# 获取System相关配置
async def System() -> SystemConfigModel:
    data = await get_all_from_db()
    return SystemConfigModel(**data)


@cached_before("all_config", 3600)
async def get_all_from_db():
    result = await Config.all().values_list("key", "value")
    return dict(result)
