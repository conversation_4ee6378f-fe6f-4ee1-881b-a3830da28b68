services:
  server:
    build: ./server
    env_file:
      - .env
    volumes:
      - .data/server:/data
      - ./server:/server
    restart: unless-stopped
    stop_grace_period: 120s
    depends_on:
      - db
      - cache
    ports:
      - "8020:8000"

  crawl:
    build: ./crawler4ai-docker
    volumes:
      - ./crawler4ai-docker:/app
    ports:
      - "11235:11235"

  cache:
    image: redis:7.0
    restart: unless-stopped
    volumes:
      - .data/redis:/data

  db:
    image: mysql:8.0
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-zcws}
      - MYSQL_DATABASE=${MYSQL_DATABASE:-zcws}
      - MYSQL_USER=${MYSQL_USER:-zcws}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-zcws}
    volumes:
      - .data/mysql:/var/lib/mysql
    ports:
      - "3308:3306"
    command: --collation-server=utf8mb4_bin
